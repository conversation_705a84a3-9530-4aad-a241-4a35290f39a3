<table class="table st-cart-table">
    <tbody>
        <?php $__empty_1 = true; $__currentLoopData = $_cart['rows']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
        <tr>
            <td>
                <div class="media">
                    <div class="media-left">
                        <a href="<?php echo e(route('product.show', ['id'=>$row['product_id']])); ?>">
                            <img class="media-object" src="<?php echo e($row['product']['img_cover']); ?>">
                        </a>
                    </div>
                    <div class="media-body">
                        <h5 class="media-heading">
                            <a href="<?php echo e(route('product.show', ['id'=>$row['product_id']])); ?>"><?php echo e($row['product']['title']); ?></a>
                        </h5>
                        <p class="st-itemcode"><small><?php echo app('translator')->get('Item code #'); ?>: <?php echo e($row['product']['sku']); ?></small></p>
                        <p class="st-specs">
                            <small>
                                <?php $__currentLoopData = $row['product']['specs']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $spec): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php echo e($spec['name']); ?>:<?php echo e($spec['pivot']['spec_value']); ?> 
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </small>
                        </p>
                        <p class="st-price">
                            <span><?php echo e($_current_currency_name); ?> <?php echo e($row['product_price_now']); ?></span> x 
                            <input name="qty" value="<?php echo e($row['qty']); ?>" type="number" onchange="Util.updatecart(<?php echo e($row['product_id']); ?>, this.value);" onkeyup="Util.updatecart(<?php echo e($row['product_id']); ?>, this.value);" />
                        </p>
                    </div>
                </div>
            </td>
            <td class="st-wholesale hidden-xs">
                <table class="table">
                    <tbody>
                        <?php $__currentLoopData = $row['product']['wholesale_set']['num']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $wholesale_num): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td>
                                <?php if($loop->last): ?> <font>≥</font> <?php else: ?> <font>≥</font> <?php endif; ?>
                                <?php echo e($wholesale_num); ?>

                            </td>
                            <td><?php echo e($_current_currency_name); ?> <?php echo e($row['product']['wholesale_set']['price'][$loop->index]); ?></td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </td>
            <td>
                <button class="btn btn-default btn-xs glyphicon glyphicon-trash st-removecart" onclick="Util.removecart(<?php echo e($row['product_id']); ?>)"></button>
            </td>
        </tr>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
    <p class="text-center"><?php echo app('translator')->get('No data.'); ?> <a href='/'><?php echo app('translator')->get('Go Home'); ?></a></p>
    <?php endif; ?>
</tbody>
</table>
<?php /**PATH D:\wwwroot\shiptobuy_guanwang\resources\views\themes\default/layouts/includes/shoppingcart.blade.php ENDPATH**/ ?>