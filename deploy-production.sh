#!/bin/bash

# StrongShop 生产环境部署脚本
# 适用于 CentOS 7 + 宝塔面板环境
# 域名: www.shiptobuy.com

echo "=========================================="
echo "StrongShop 生产环境部署脚本"
echo "=========================================="

# 设置变量
PROJECT_PATH="/www/wwwroot/shiptobuy.com"
BACKUP_PATH="/www/backup/shiptobuy_$(date +%Y%m%d_%H%M%S)"
PHP_PATH="/www/server/php/72/bin/php"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用root用户运行此脚本"
    exit 1
fi

# 检查项目目录是否存在
if [ ! -d "$PROJECT_PATH" ]; then
    echo "错误: 项目目录 $PROJECT_PATH 不存在"
    exit 1
fi

echo "1. 创建备份..."
mkdir -p /www/backup
cp -r $PROJECT_PATH $BACKUP_PATH
echo "备份已创建: $BACKUP_PATH"

echo "2. 进入项目目录..."
cd $PROJECT_PATH

echo "3. 检查.env文件..."
if [ ! -f ".env" ]; then
    echo "创建.env文件..."
    cp .env.example .env
    echo "请手动编辑.env文件设置生产环境配置"
    echo "主要配置项:"
    echo "  APP_ENV=production"
    echo "  APP_DEBUG=false"
    echo "  APP_URL=https://www.shiptobuy.com"
    echo "  数据库配置等..."
    read -p "配置完成后按回车继续..."
fi

echo "4. 安装Composer依赖..."
if command -v composer &> /dev/null; then
    composer install --no-dev --optimize-autoloader --no-interaction
else
    echo "错误: 未找到composer命令"
    exit 1
fi

echo "5. 生成应用密钥..."
$PHP_PATH artisan key:generate --force

echo "6. 清除缓存..."
$PHP_PATH artisan cache:clear
$PHP_PATH artisan config:clear
$PHP_PATH artisan route:clear
$PHP_PATH artisan view:clear

echo "7. 生产环境优化..."
$PHP_PATH artisan config:cache
$PHP_PATH artisan route:cache
$PHP_PATH artisan view:cache
$PHP_PATH artisan optimize

echo "8. 创建存储链接..."
$PHP_PATH artisan storage:link

echo "9. 设置文件权限..."
chown -R www:www $PROJECT_PATH
chmod -R 755 $PROJECT_PATH
chmod -R 777 $PROJECT_PATH/storage
chmod -R 777 $PROJECT_PATH/bootstrap/cache

echo "10. 删除安装文件..."
if [ -d "$PROJECT_PATH/public/install" ]; then
    rm -rf $PROJECT_PATH/public/install
    echo "已删除安装目录"
fi

echo "11. 检查必要的目录..."
mkdir -p $PROJECT_PATH/storage/logs
mkdir -p $PROJECT_PATH/storage/framework/cache
mkdir -p $PROJECT_PATH/storage/framework/sessions
mkdir -p $PROJECT_PATH/storage/framework/views
mkdir -p $PROJECT_PATH/storage/app/public

echo "12. 重启服务..."
systemctl reload nginx
systemctl reload php-fpm

echo "=========================================="
echo "部署完成！"
echo "=========================================="
echo "备份位置: $BACKUP_PATH"
echo "项目路径: $PROJECT_PATH"
echo "网站地址: https://www.shiptobuy.com"
echo ""
echo "请检查以下项目:"
echo "1. 访问网站确认前台正常"
echo "2. 访问后台管理确认功能正常"
echo "3. 测试支付功能"
echo "4. 检查SSL证书配置"
echo "5. 配置定时任务（如需要）"
echo ""
echo "如有问题，可以使用备份恢复:"
echo "rm -rf $PROJECT_PATH"
echo "mv $BACKUP_PATH $PROJECT_PATH"
echo "chown -R www:www $PROJECT_PATH"
