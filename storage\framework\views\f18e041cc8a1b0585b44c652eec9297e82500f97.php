<?php $__env->startPush('styles'); ?>
<style></style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script></script>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="st-h15"></div>
<form class="layui-form" action="">
    <input name="id" type="hidden" value="<?php echo e($model->id); ?>" />
    <div class="layui-row">
        <div class="layui-col-xs11">
            <div class="layui-form-item">
                <label class="layui-form-label st-form-input-required"><i class="layui-icon layui-icon-help st-form-tip-help"></i><?php echo e($model->getAttributeLabel('title')); ?></label>
                <div class="layui-input-block">
                    <input type="text" name="title" value="<?php echo e($model->title); ?>" autocomplete="off" placeholder="" class="layui-input">
                    <div class="layui-word-aux st-form-tip"></div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><i class="layui-icon layui-icon-help st-form-tip-help"></i><?php echo e($model->getAttributeLabel('desc')); ?></label>
                <div class="layui-input-block">
                    <input type="text" name="desc" value="<?php echo e($model->desc); ?>" autocomplete="off" placeholder="" class="layui-input">
                    <div class="layui-word-aux st-form-tip"></div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label st-form-input-required"><i class="layui-icon layui-icon-help st-form-tip-help"></i><?php echo e($model->getAttributeLabel('code')); ?></label>
                <div class="layui-input-block">
                    <input type="text" name="code" value="<?php echo e($model->code); ?>" autocomplete="off" placeholder="" class="layui-input">
                    <div class="layui-word-aux st-form-tip"></div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label st-form-input-required"><i class="layui-icon layui-icon-help st-form-tip-help"></i><?php echo e($model->getAttributeLabel('status')); ?></label>
                <div class="layui-input-block">
                    <input type="radio" name="status" value="1" title="启用" <?php if($model->status==1): ?>checked <?php endif; ?>>
                    <input type="radio" name="status" value="2" title="禁用" <?php if($model->status==2): ?>checked <?php endif; ?>>
                    <div class="layui-word-aux st-form-tip"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="layui-form-item st-form-submit-btn">
        <div class="layui-input-block">
            <button type="submit" class="layui-btn" lay-submit="" lay-filter="ST-SUBMIT">立即提交</button>
            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
        </div>
    </div>
</form>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts_bottom'); ?>
<script>
    !function () {
        //...
    }();
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('strongadmin::layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\wwwroot\shiptobuy_guanwang\resources\views/strongadmin/shippingOption/form.blade.php ENDPATH**/ ?>