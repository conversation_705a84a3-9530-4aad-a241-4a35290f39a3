-- ========================================
-- StrongShop 新增支付方式 SQL 语句
-- ========================================
-- 请通过 phpMyAdmin 或其他数据库管理工具执行以下SQL语句
-- 数据库: shiptobuy_com_gw

-- 1. 首先检查是否已存在这些支付方式
SELECT id, title, code, status FROM st_payment_option WHERE code IN ('paypal_new', 'contact_service');

-- 如果上面的查询没有返回结果，则执行下面的插入语句

-- 2. 添加新版PayPal支付方式（使用最新PayPal API）
INSERT INTO `st_payment_option` (`title`, `desc`, `code`, `status`, `more`, `created_at`, `updated_at`) VALUES 
('PayPal (新版)', '使用最新PayPal API的支付方式，支持更好的用户体验', 'paypal_new', 1, 
'{"env": "sandbox", "rate": null, "currency": null, "client_id": "your_paypal_client_id_here", "client_secret": "your_paypal_client_secret_here"}', 
NOW(), NOW());

-- 3. 添加联系客服支付方式
INSERT INTO `st_payment_option` (`title`, `desc`, `code`, `status`, `more`, `created_at`, `updated_at`) VALUES 
('其他支付找客服', '联系客服获取更多支付方式选项，支持微信、支付宝等多种支付方式', 'contact_service', 1, 
'{"rate": null, "currency": null, "qr_code": "", "description": "如需使用其他支付方式，请扫描下方二维码联系客服。\\n\\n我们支持：\\n• 微信支付\\n• 支付宝\\n• 银行转账\\n• 其他支付方式\\n\\n客服将为您提供详细的支付指导。"}', 
NOW(), NOW());

-- 4. 验证添加结果
SELECT id, title, code, status, created_at FROM `st_payment_option` ORDER BY id;

-- ========================================
-- 执行完成后的操作说明：
-- ========================================
-- 1. 在管理后台配置PayPal新版的Client ID和Client Secret
-- 2. 在管理后台上传联系客服的二维码图片
-- 3. 安装PayPal新版SDK: composer require paypal/paypal-checkout-sdk
-- 4. 清理缓存: php artisan cache:clear
-- 5. 测试新的支付方式是否在前台显示
