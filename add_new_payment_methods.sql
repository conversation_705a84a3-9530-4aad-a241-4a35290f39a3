-- 添加新的支付方式到StrongShop系统
-- 执行前请确保备份数据库

-- 检查是否已存在这些支付方式
SELECT COUNT(*) as existing_count FROM `st_payment_option` WHERE `code` IN ('paypal_new', 'contact_service');

-- 如果上面的查询结果为0，则执行下面的插入语句

-- 添加新版PayPal支付方式（使用最新PayPal API）
INSERT INTO `st_payment_option` (`title`, `desc`, `code`, `status`, `more`, `created_at`, `updated_at`) VALUES
('PayPal (新版)', '使用最新PayPal API的支付方式，支持更好的用户体验', 'paypal_new', 1,
'{"env": "sandbox", "rate": null, "currency": null, "client_id": "your_paypal_client_id_here", "client_secret": "your_paypal_client_secret_here"}',
NOW(), NOW());

-- 添加联系客服支付方式
INSERT INTO `st_payment_option` (`title`, `desc`, `code`, `status`, `more`, `created_at`, `updated_at`) VALUES
('其他支付找客服', '联系客服获取更多支付方式选项，支持微信、支付宝等多种支付方式', 'contact_service', 1,
'{"rate": null, "currency": null, "qr_code": "", "description": "如需使用其他支付方式，请扫描下方二维码联系客服。\\n\\n我们支持：\\n• 微信支付\\n• 支付宝\\n• 银行转账\\n• 其他支付方式\\n\\n客服将为您提供详细的支付指导。"}',
NOW(), NOW());

-- 查看添加结果
SELECT id, title, code, status FROM `st_payment_option` ORDER BY id;
