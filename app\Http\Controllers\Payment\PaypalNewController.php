<?php

/**
 * StrongShop
 * <AUTHOR> <<EMAIL>>
 * @license http://www.strongshop.cn/license/
 * @copyright StrongShop Software
 */

namespace App\Http\Controllers\Payment;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Models\Order\Order;
use App\Repositories\OrderRepository;
use App\Models\PaymentOption;

// PayPal新版SDK类（需要安装 paypal/paypal-checkout-sdk）
// 注意：如果SDK未安装，此控制器将返回错误信息

class PaypalNewController extends Controller
{
    /**
     * 检测PayPal配置是否正确
     */
    public function testConnection(Request $request)
    {
        try {
            $model = PaymentOption::where('code', 'paypal_new')->first();
            if (!$model) {
                return response()->json([
                    'code' => 4001,
                    'message' => 'PayPal支付方式未配置'
                ]);
            }

            // 检查必要配置
            $clientId = $model->more['client_id'] ?? '';
            $clientSecret = $model->more['client_secret'] ?? '';
            $env = $model->more['env'] ?? 'sandbox';

            if (empty($clientId) || empty($clientSecret)) {
                return response()->json([
                    'code' => 4002,
                    'message' => 'PayPal Client ID 或 Client Secret 未配置'
                ]);
            }

            // 尝试获取访问令牌
            $accessToken = $this->getAccessToken($model);

            if ($accessToken) {
                return response()->json([
                    'code' => 200,
                    'message' => 'PayPal配置正确，连接成功！',
                    'data' => [
                        'environment' => $env,
                        'client_id' => substr($clientId, 0, 10) . '...',
                        'status' => 'connected'
                    ]
                ]);
            } else {
                return response()->json([
                    'code' => 4003,
                    'message' => 'PayPal连接失败，请检查配置'
                ]);
            }

        } catch (\Exception $e) {
            return response()->json([
                'code' => 5001,
                'message' => 'PayPal连接测试失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取PayPal API基础URL
     */
    private function getPayPalBaseUrl($env)
    {
        return $env === 'live'
            ? 'https://api.paypal.com'
            : 'https://api.sandbox.paypal.com';
    }

    /**
     * 获取PayPal访问令牌
     */
    private function getAccessToken($model)
    {
        $clientId = $model->more['client_id'] ?? '';
        $clientSecret = $model->more['client_secret'] ?? '';
        $env = $model->more['env'] ?? 'sandbox';

        if (empty($clientId) || empty($clientSecret)) {
            throw new \Exception('PayPal凭据未配置');
        }

        $baseUrl = $this->getPayPalBaseUrl($env);
        $url = $baseUrl . '/v1/oauth2/token';

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_USERPWD, $clientId . ':' . $clientSecret);
        curl_setopt($ch, CURLOPT_POSTFIELDS, 'grant_type=client_credentials');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Accept: application/json',
            'Accept-Language: en_US'
        ]);

        // 添加SSL和超时设置
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        if ($response === false) {
            throw new \Exception('PayPal API请求失败: ' . $curlError);
        }

        if ($httpCode !== 200) {
            throw new \Exception('获取PayPal访问令牌失败: HTTP ' . $httpCode . ', Response: ' . $response);
        }

        $data = json_decode($response, true);
        if (!isset($data['access_token'])) {
            throw new \Exception('PayPal返回的令牌格式错误: ' . $response);
        }

        return $data['access_token'];
    }

    /**
     * 创建PayPal订单
     */
    public function createOrder(Request $request)
    {
        try {
            $orderId = $request->orderId;



            // 获取订单信息
            $order = Order::query()->where('id', $orderId)
                    ->with('orderProducts')
                    ->first();

            if (!$order) {
                return response()->json(['error' => '订单不存在'], 404);
            }

            if (!in_array($order->order_status, [Order::STATUS_UNPAID, Order::STATUS_PAY_FAILED])) {
                return response()->json(['error' => '订单状态无效'], 400);
            }

            // 获取支付方式配置
            $model = PaymentOption::where('code', 'paypal_new')->where('status', 1)->first();
            if (!$model) {
                return response()->json(['error' => '支付方式不存在'], 400);
            }

            $accessToken = $this->getAccessToken($model);
            $env = $model->more['env'] ?? 'sandbox';
            $baseUrl = $this->getPayPalBaseUrl($env);



            // 构建客户信息字符串，格式：订单号-邮箱-电话
            $customerInfo = $order->order_no;
            if ($order->email) {
                $customerInfo .= '-' . $order->email;
            }
            if ($order->phone) {
                $customerInfo .= '-' . $order->phone;
            }

            // 构建订单数据
            $orderData = [
                'intent' => 'CAPTURE',
                'purchase_units' => [
                    [
                        'reference_id' => $order->order_no,
                        'amount' => [
                            'currency_code' => $order->currency_code,
                            'value' => number_format($order->order_amount, 2, '.', '')
                        ],
                        'description' => $customerInfo,
                        'custom_id' => $customerInfo  // 也添加到custom_id字段
                    ]
                ],
                'application_context' => [
                    'return_url' => route('paypal_new.success'),
                    'cancel_url' => route('paypal_new.cancel'),
                    'brand_name' => config('app.name'),
                    'landing_page' => 'BILLING',
                    'user_action' => 'PAY_NOW'
                ]
            ];



            // 调用PayPal API创建订单
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $baseUrl . '/v2/checkout/orders');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($orderData));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $accessToken,
                'PayPal-Request-Id: ' . uniqid()
            ]);

            // 添加SSL和超时设置
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);



            if ($response === false) {
                \Log::error('PayPal创建订单cURL失败: ' . $curlError);
                return response()->json(['error' => 'PayPal连接失败'], 500);
            }

            if ($httpCode !== 201) {
                \Log::error('PayPal创建订单失败: HTTP ' . $httpCode . ', Response: ' . $response);
                return response()->json(['error' => '创建PayPal订单失败'], 500);
            }

            $paypalOrder = json_decode($response, true);

            // 保存PayPal订单ID到session
            session(['paypal_order_id' => $paypalOrder['id'], 'local_order_id' => $order->id]);

            return response()->json([
                'id' => $paypalOrder['id'],
                'status' => $paypalOrder['status'],
                'links' => $paypalOrder['links']
            ]);

        } catch (\Exception $e) {
            Log::error('PayPal新版创建订单失败: ' . $e->getMessage());
            return response()->json(['error' => '创建支付订单失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 支付页面
     */
    public function pay(Request $request)
    {
        $orderId = $request->orderId;
        
        // 获取订单信息
        $order = Order::query()->where('id', $orderId)
                ->with('orderProducts')
                ->first();
                
        if (!$order) {
            abort(404);
        }
        
        if (!in_array($order->order_status, [Order::STATUS_UNPAID, Order::STATUS_PAY_FAILED])) {
            abort(201, __('Invalid'));
        }

        // 获取支付方式配置
        $model = PaymentOption::where('code', 'paypal_new')->where('status', 1)->first();
        if (!$model) {
            abort(201, __('Payment not exists'));
        }

        $clientId = $model->more['client_id'] ?? '';
        
        return view('payment.paypal_new', [
            'order' => $order,
            'payment' => $model,
            'client_id' => $clientId
        ]);
    }

    /**
     * 支付成功回调
     */
    public function success(Request $request)
    {
        try {
            $paypalOrderId = $request->token;
            $localOrderId = session('local_order_id');

            if (!$paypalOrderId || !$localOrderId) {
                return redirect()->route('user.my.orders')->with('error', '支付信息丢失');
            }

            // 获取支付方式配置
            $model = PaymentOption::where('code', 'paypal_new')->where('status', 1)->first();
            if (!$model) {
                return redirect()->route('user.my.orders')->with('error', '支付方式不存在');
            }

            $accessToken = $this->getAccessToken($model);
            $env = $model->more['env'] ?? 'sandbox';
            $baseUrl = $this->getPayPalBaseUrl($env);

            // 捕获支付
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $baseUrl . '/v2/checkout/orders/' . $paypalOrderId . '/capture');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, '{}');
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $accessToken,
                'PayPal-Request-Id: ' . uniqid()
            ]);

            // 添加SSL和超时设置
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);

            if ($response === false) {
                Log::error('PayPal捕获支付cURL失败: ' . $curlError);
                return redirect()->route('user.my.orders')->with('error', 'PayPal连接失败');
            }

            if ($httpCode === 201) {
                $paypalResponse = json_decode($response, true);

                if ($paypalResponse['status'] === 'COMPLETED') {
                    // 获取本地订单
                    $order = Order::find($localOrderId);
                    if ($order) {
                        $transactionId = $paypalResponse['purchase_units'][0]['payments']['captures'][0]['id'] ?? '';
                        $paidAmount = $paypalResponse['purchase_units'][0]['payments']['captures'][0]['amount']['value'] ?? '';
                        $paidCurrency = $paypalResponse['purchase_units'][0]['payments']['captures'][0]['amount']['currency_code'] ?? 'USD';

                        // 标记订单为已支付
                        OrderRepository::paid($order->order_no, $transactionId, $paidAmount, $paidCurrency);

                        // 清除session
                        session()->forget(['paypal_order_id', 'local_order_id']);

                        // 跳转到订单详情页面
                        return redirect()->route('user.my.order.detail', ['orderId' => $order->id])
                            ->with('success', '支付成功！订单已完成支付。');
                    }
                }
            }

            return redirect()->route('user.my.orders')->with('error', '支付处理失败');

        } catch (\Exception $e) {
            Log::error('PayPal新版支付成功处理失败: ' . $e->getMessage());
            return redirect()->route('user.my.orders')->with('error', '支付处理异常');
        }
    }

    /**
     * 支付取消
     */
    public function cancel(Request $request)
    {
        // 清除session
        session()->forget(['paypal_order_id', 'local_order_id']);

        return redirect()->route('user.my.orders')->with('info', '支付已取消');
    }

    /**
     * Webhook处理（可选）
     */
    public function webhook(Request $request)
    {
        try {
            // 这里可以添加webhook验证和处理逻辑
            Log::channel('paypal')->info('PayPal新版Webhook接收: ' . $request->getContent());

            return response('OK', 200);
        } catch (\Exception $e) {
            Log::error('PayPal新版Webhook处理失败: ' . $e->getMessage());
            return response('Error', 500);
        }
    }

}
