<div class="container">
    <div class="row">
        <!--主体左侧-产品图册信息-->
        <div class="col-sm-12 col-md-7 col-lg-5">
            <!--PC端轮播图-->
            <div class="st-detail-img hidden-xs hidden-sm">
 <div class="st-detail-img-left pull-left">
    <ul>
        <?php if($row->mp4): ?>
        <li class="video-thumb active">
            <video muted loop playsinline
                poster="<?php echo e($row->video_thumbnail ?? $row->asset_img_photos[0]['src']); ?>"
                style="width:100%;height:100%;object-fit:cover">
                <source src="<?php echo e($row->mp4); ?>" type="video/mp4">
            </video>
            <div class="video-icon">▶</div>
        </li>
        <?php endif; ?>
        <?php $__currentLoopData = $row->asset_img_photos; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $photo): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <li <?php if(!$row->mp4 && $loop->first): ?>class="active" <?php endif; ?>>
            <img src="<?php echo e($photo['src']); ?>" data-src="<?php echo e($photo['src']); ?>" alt="<?php echo e($row->title); ?>">
        </li>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </ul>
</div>
 
                <div class="st-detail-img-right pull-right">
                  
<div class="pic">
    <?php if($row->mp4): ?>
    <video 
        id="mainVideo"
        muted loop controls playsinline
        poster="<?php echo e($row->video_thumbnail ?? $row->asset_img_photos[0]['src']); ?>"
        style="width:100%"
    >
        <source src="<?php echo e($row->mp4); ?>" type="video/mp4">
    </video>
    <?php else: ?>
    <?php if(isset($row->asset_img_photos[0]['src'])): ?>
    <img src="<?php echo e($row->asset_img_photos[0]['src']); ?>" alt="<?php echo e($row->title); ?>">
    <?php endif; ?>
    <?php endif; ?>
    <div class="magnify"></div>
</div>                  
                  
                  
                    <div class="bigpic">
                        <?php if(isset($row->asset_img_photos[0]['src'])): ?>
                        <img src="<?php echo e($row->asset_img_photos[0]['src']); ?>" alt="<?php echo e($row->title); ?>"  >
                        <?php endif; ?>
                    </div>
                    <div class="st-detail-img-right-share">
                        <!--分享
                        <p class="st-share"><?php echo app('translator')->get('Share to'); ?>: 
                            <a href="#" class="bi-facebook"></a>
                            <a href="#" class="bi-twitter"></a>
                            <a href="#" class="bi-instagram"></a>
                            <a href="#" class="bi-linkedin"></a>
                        </p>-->
                    </div>
                </div>
                <div class="clearfix"></div>
            </div>
            <!--移动端轮播图-->
            <div class="st-detail-img visible-xs visible-sm">
                <div class="swiper-container">
                    <div class="swiper-wrapper">
                        <?php $__currentLoopData = $row->asset_img_photos; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $photo): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="swiper-slide"><img src="<?php echo e($photo['src']); ?>" alt="<?php echo e($row->title); ?>" class="img-responsive"></div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <!-- Add Pagination -->
                    <div class="swiper-pagination"></div>
                </div>
            </div>
        </div>
        <div class="col-sm-12 col-md-5 col-lg-7">
            <div class="row">
                <!--主体右侧-产品属性信息-->
                <div class="col-sm-12 col-lg-7">
                    <div class="st-detail-attr">
                        <h3><?php echo e($row->title); ?></h3>
                        <dl class="st-general st-itemcode">
                            <dd><?php echo app('translator')->get('Item code #'); ?>:<?php echo e($row->sku); ?></dd>
                        </dl>
                        <dl class="st-general st-price">
                            <dt><?php echo e($_current_currency_name); ?> <?php echo e($row->sale_price); ?><del><?php echo e($_current_currency_name); ?> <?php echo e($row->original_price); ?></del></dt>
                        </dl>
                        <script>
document.addEventListener("DOMContentLoaded", function(){
    // 修改价格显示（含原价）
    document.querySelectorAll('.st-general.st-price dt').forEach(e => {
        // 同时处理现价和原价
        e.innerHTML = e.innerHTML.replace(/(\d+\.\d{3})/g, m => (+m).toFixed(2));
    });
});
</script>
                        <?php $__currentLoopData = $price_sepcs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $price_sepc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <dl class="st-attr <?php if($price_sepc['is_show_img'] ==1): ?>st-picture <?php endif; ?>">
                            <dt><?php echo e($price_sepc['name']); ?>:</dt>
                            <?php $__currentLoopData = $price_sepc['spu_specs']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $spu_spec): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <dd title="<?php echo e($spu_spec['pivot']['spec_value']); ?>" data-toggle="tooltip" data-placement="top" data-spec="<?php echo e($spu_spec['pivot']['spec_value']); ?>" <?php if($price_sepc['pivot']['spec_value'] == $spu_spec['pivot']['spec_value']): ?> class="active" <?php endif; ?>>
                                <?php if($price_sepc['is_show_img'] ==1): ?>
                                <img src="<?php echo e($spu_spec['product_img']); ?>" />
                                <?php else: ?>
                                <?php echo e($spu_spec['pivot']['spec_value']); ?>

                                <?php endif; ?>
                            </dd>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </dl>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <!--主体右侧-操作按钮-->
                <div class="col-sm-12 col-lg-5 st-detail-btn">
                    <div class="st-detail-btn-right">
                        <!--批发信息-->
                        <?php if(count($row->wholesale_set['num'])): ?>
                        <div class="st-whosale">
                            <table class="table">
                                <!--<caption><?php echo app('translator')->get('Wholesale'); ?></caption>-->
                                <thead>
                                    <tr>
                                        <th><?php echo app('translator')->get('QTY'); ?></th>
                                        <th><?php echo app('translator')->get('Price'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $row->wholesale_set['num']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $wholesale_num): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <?php if($loop->last): ?> <font>≥</font> <?php else: ?> <font>≥</font> <?php endif; ?>
                                            <?php echo e($wholesale_num); ?>

                                        </td>
                                        <td><?php echo e($_current_currency_name); ?> <?php echo e($row->wholesale_set['price'][$loop->index]); ?></td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                        <?php endif; ?>
                        <!--库存状态-->
                        <?php if($row->stock_status == 1 && $row->stock >0): ?>
                        <p class="st-instock"><?php echo e($row->stock); ?> <?php echo app('translator')->get('in stock'); ?>.</p>
                        <?php else: ?>
                        <p class="st-stockout"><?php echo app('translator')->get('Stock Out.'); ?></p>
                        <?php endif; ?>
                        <!--购物数量-->
                        <p class="st-qty">
                            <?php echo app('translator')->get('QTY'); ?>: <input id="ST-QTY" name="qty" value="1" type="number" min="1" max="99999" />
                        </p>
                        <!--按钮-->
                        <p class="st-btn">
                            <button class="btn btn-primary addtocart" onclick="Util.addtocart(<?php echo e($row->id); ?>, $('#ST-QTY').val())">
                                <span class="glyphicon glyphicon-shopping-cart"></span><?php echo app('translator')->get('ADD TO CART'); ?>
                            </button>
                            <button class="btn btn-info buynow" onclick="Util.buyNow(<?php echo e($row->id); ?>, $('#ST-QTY').val())">
                                <span class="glyphicon glyphicon-hand-right"></span><?php echo app('translator')->get('Buy Now'); ?>
                            </button>
                            <button class="btn btn-default addtowishlist" onclick="Util.addToWishList(<?php echo e($row->id); ?>)">
                                <span class="glyphicon <?php if($row->is_collected): ?>glyphicon-heart <?php else: ?> glyphicon-heart-empty <?php endif; ?>"></span><?php echo app('translator')->get('Add to wish list'); ?>
                            </button>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="st-h50"></div>
<div class="container">
    <div class="st-main-detail">
        <!-- Nav tabs -->
        <ul class="nav nav-tabs" role="tablist">
            <li role="presentation" class="active"><a href="#Details" role="tab" data-toggle="tab" class="bi-stickies"> <?php echo app('translator')->get('Details'); ?></a></li>
                <!--注释掉选项卡多类型    <?php if(!empty($row->related_accessories_sku)): ?>
            <li role="presentation"><a href="#Accessories"role="tab" data-toggle="tab" class="bi-tools"> <?php echo app('translator')->get('Accessories'); ?></a></li>
            <?php endif; ?>
            <li role="presentation"><a href="#Delivery"role="tab" data-toggle="tab" class="bi-map"> <?php echo app('translator')->get('Delivery & Tax'); ?></a></li>
            <li role="presentation"><a href="#Returns" role="tab" data-toggle="tab" class="bi-bootstrap-reboot"> <?php echo app('translator')->get('Returns'); ?></a></li>
            <li role="presentation"><a href="#Reviews" role="tab" data-toggle="tab" class="bi-emoji-smile"> <?php echo app('translator')->get('Reviews'); ?></a></li>-->
        </ul>
        <!-- Tab panes -->
        <div class="tab-content">


            <div class="st-h10"></div>
                      <!-- 新增视频展示区域-->
<?php if($row->mp4): ?>
<div class="video-container" style="margin: 20px 0;">
    <div class="embed-responsive embed-responsive-16by9">
        <video 
            id="productVideo"
            class="embed-responsive-item"
            controls
            autoplay   <!-- 添加自动播放属性 -->
            muted      <!-- 添加静音属性 -->
            playsinline <!-- 移动端内联播放 -->
            controlsList="nodownload"
            poster="<?php echo e($row->video_thumbnail ?? '/placeholder.jpg'); ?>"
            preload="auto"  <!-- 优化预加载 -->
            style="background: #000;"
        >
            <source src="<?php echo e($row->mp4); ?>" type="video/mp4">
            <source src="<?php echo e(str_replace('.mp4', '.webm', $row->mp4)); ?>" type="video/webm">
            <div class="video-fallback">
                <p><?php echo app('translator')->get('Your browser does not support HTML5 video.'); ?></p>
                <a href="<?php echo e($row->mp4); ?>" download class="btn btn-primary">
                    <?php echo app('translator')->get('Download Video'); ?>
                </a>
            </div>
        </video>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const video = document.getElementById('productVideo');
        
        // 自动播放处理逻辑
        const tryAutoPlay = () => {
            const promise = video.play();
            if (promise !== undefined) {
                promise.catch(error => {
                    // 如果自动播放被阻止，显示播放按钮
                    if(error.name === "NotAllowedError") {
                        video.controls = true;
                        video.muted = false;
                    }
                });
            }
        }

        // 移动端处理
        if ('ontouchstart' in window) {
            video.controls = false;
            // 添加点击区域
            const playOverlay = document.createElement('div');
            playOverlay.style.cssText = `
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 60px;
                height: 60px;
                background: rgba(0,0,0,0.5);
                border-radius: 50%;
                cursor: pointer;
                z-index: 100;
            `;
            playOverlay.innerHTML = `<svg viewBox="0 0 24 24" style="width:40px;height:40px;margin:10px;fill:#fff">
                <path d="M8 5v14l11-7z"/>
            </svg>`;
            video.parentElement.style.position = 'relative';
            video.parentElement.appendChild(playOverlay);

            playOverlay.addEventListener('click', () => {
                playOverlay.remove();
                video.controls = true;
                video.muted = false;
                tryAutoPlay();
            });
        } else {
            // 桌面端直接尝试自动播放
            tryAutoPlay();
        }
    });
    </script>
</div>
<?php endif; ?>
            <div role="tabpanel" class="tab-pane active" id="Details">
                <?php if(!empty($general_sepcs)): ?>
                <table class="table table-striped">
                    <caption><?php echo app('translator')->get('Product specification'); ?></caption>
                    <tbody>
                        <?php $__currentLoopData = $general_sepcs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $general_sepc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <th><?php echo e($general_sepc['name']); ?></th>
                            <td><?php echo e($general_sepc['pivot']['spec_value']); ?></td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
                
                <div class="st-h50"></div>
                <?php endif; ?>
                <?php if($row->details): ?>
                <?php echo $row->details; ?>

                <?php else: ?>
                <?php $__currentLoopData = $row->asset_img_photos; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $photo): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <img src="<?php echo e($photo['src']); ?>" alt="<?php echo e($row->title); ?>" class="img-responsive" />
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </div>
            <?php if(!empty($row->related_accessories_sku)): ?>
            <div role="tabpanel" class="tab-pane" id="Accessories">
                <table class="table st-cart-table">
                    <tbody>
                        <?php $__currentLoopData = $row->related_accessories_sku; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $related_accessory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td>
                                <div class="media">
                                    <div class="media-left">
                                        <a href="<?php echo e(route('product.show', ['id'=>$related_accessory->id])); ?>">
                                            <img class="media-object" src="<?php echo e($related_accessory->img_cover); ?>" alt="<?php echo e($related_accessory->title); ?>" title="<?php echo e($related_accessory->title); ?>" />
                                        </a>
                                    </div>
                                    <div class="media-body">
                                        <h5 class="media-heading"><a href="<?php echo e(route('product.show', ['id'=>$related_accessory->id])); ?>"><?php echo e($related_accessory->title); ?></a></h5>
                                        <p class="st-itemcode"> <small><?php echo app('translator')->get('Item code #'); ?>: <?php echo e($related_accessory->sku); ?></small></p>
                                        <p class="st-price">
                                            <?php echo e($_current_currency_name); ?> <?php echo e($related_accessory->sale_price); ?> x 
                                            <input value="1" name='accNum' type="number" />
                                        </p>
                                    </div>
                                </div>
                            </td>
                            <td class="st-wholesale hidden-xs">
                                <table class="table">
                                    <tbody>
                                        <?php $__currentLoopData = $related_accessory->wholesale_set['num']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $wholesale_num): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <?php if($loop->last): ?> <font>≥</font> <?php else: ?> <font>≥</font> <?php endif; ?>
                                                <?php echo e($wholesale_num); ?>

                                            </td>
                                            <td><?php echo e($_current_currency_name); ?> <?php echo e($related_accessory->wholesale_set['price'][$loop->index]); ?></td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </td>
                            <td>
                                <button onclick="Util.addtocart(<?php echo e($related_accessory->id); ?>, $(this).parentsUntil('tr').siblings().find('input[name=accNum]').val()); $(this).children('i').removeClass('bi-cart').addClass('bi-cart-fill');" class="btn btn-default btn-xs"> 
                                    <i class="bi-cart"></i> 
                                    <span class="hidden-xs"><?php echo app('translator')->get('Add to cart'); ?></span>
                                    <span class="visible-xs-inline"><?php echo app('translator')->get('Add'); ?></span>
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
            <div role="tabpanel" class="tab-pane" id="Delivery">
            </div>
            <div role="tabpanel" class="tab-pane" id="Returns">
            </div>
            <div role="tabpanel" class="tab-pane" id="Reviews">
                <iframe src="<?php echo e(route('comment.list', ['product_id'=>$row->id,'sku'=>$row->sku,'spu'=>$row->spu,'no_header'=>1])); ?>" frameborder="0" scrolling="yes" style="width:100%;height: 800px;"></iframe>
            </div>
        </div>
    </div>
</div><?php /**PATH D:\wwwroot\shiptobuy_guanwang\resources\views\themes\default/layouts/includes/productShow.blade.php ENDPATH**/ ?>