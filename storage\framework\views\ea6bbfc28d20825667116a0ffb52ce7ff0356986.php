<?php if(isset($specs) && isset($productSpecs)): ?>
<!--价格规格-->
<fieldset class="layui-elem-field">
    <legend>价格规格<font class="st-sepc-tip">（作为产品价格属性选择）</font></legend>
    <div class="layui-field-box">
        <div class="layui-form-item">
            <?php $__currentLoopData = $specs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $spec): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if($spec['spec_type']==1): ?>
            <label class="layui-form-label st-form-input-required"><?php echo e($spec->name_label); ?></label>
            <div class="layui-input-inline">
                <?php if($spec['input_type'] == 1): ?>
                <select name="productSpec" lay-filter='productSpec' data-specId="<?php echo e($spec->id); ?>" data-specType="<?php echo e($spec->spec_type); ?>">
                    <option value=""> -- </option>
                    <?php $__currentLoopData = $spec['select_values_array']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $select_value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($select_value); ?>" <?php if(isset($productSpecs[$spec->id]) && $productSpecs[$spec->id] == $select_value): ?>selected <?php endif; ?>><?php echo e($select_value); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
                <?php elseif($spec['input_type'] == 2): ?>
                <input type="text" name="productSpec" value="<?php echo e($productSpecs[$spec->id] ?? ''); ?>" data-specId="<?php echo e($spec->id); ?>" data-specType="<?php echo e($spec->spec_type); ?>" class="layui-input">
                <?php endif; ?>
            </div>
            <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</fieldset>
<!--普通规格-->
<fieldset class="layui-elem-field">
    <legend>普通规格 <font class="st-sepc-tip">（作为普通产品参数显示）</font></legend>
    <div class="layui-field-box">
        <?php $__currentLoopData = $specs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $spec): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if($spec['spec_type']==2): ?>
        <div class="layui-form-item">
            <label class="layui-form-label"><?php echo e($spec->name_label); ?></label>
            <div class="layui-input-inline">
                <?php if($spec['input_type'] == 1): ?>
                <select name="productSpec" lay-filter='productSpec' data-specId="<?php echo e($spec->id); ?>" data-specType="<?php echo e($spec->spec_type); ?>">
                    <option> -- </option>
                    <?php $__currentLoopData = $spec['select_values_array']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $select_value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($select_value); ?>" <?php if(isset($productSpecs[$spec->id]) && $productSpecs[$spec->id] == $select_value): ?>selected <?php endif; ?>><?php echo e($select_value); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
                <?php elseif($spec['input_type'] == 2): ?>
                <input type="text" name="productSpec" value="<?php echo e($productSpecs[$spec->id] ?? ''); ?>" data-specId="<?php echo e($spec->id); ?>" data-specType="<?php echo e($spec->spec_type); ?>" class="layui-input">
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</fieldset>
<?php endif; ?>
<?php /**PATH D:\wwwroot\shiptobuy_guanwang\resources\views/strongadmin/product/spec.blade.php ENDPATH**/ ?>