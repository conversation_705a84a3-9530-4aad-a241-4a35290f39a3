# 生产环境部署检查清单

## 服务器环境要求 ✅
- **操作系统**: CentOS 7 ✅
- **Web服务器**: Nginx 1.20.2 ✅ (要求 >= 1.10)
- **PHP版本**: PHP 7.2 ✅ (要求 >= 7.2.5)
- **数据库**: MySQL 5.7.44 ✅ (要求 >= 5.7)
- **缓存**: Redis 7.4.3 ✅
- **管理面板**: 宝塔面板 9.4.0 ✅

## PHP扩展要求检查
确保以下PHP扩展已启用：
- [x] PDO PHP 扩展
- [x] pdo_mysql 扩展
- [x] OpenSSL PHP 扩展
- [x] cURL 扩展
- [x] GD 扩展
- [x] Fileinfo PHP 扩展
- [x] Mbstring PHP 扩展
- [x] mysqli 扩展

## 生产环境配置

### 1. 环境变量配置 (.env)
```bash
# 应用配置
APP_NAME=StrongShop
APP_ENV=production
APP_DEBUG=false
APP_URL=https://www.shiptobuy.com

# 数据库配置
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=shiptobuy_com_gw
DB_USERNAME=shiptobuy_com_gw
DB_PASSWORD=3Zjj86Cz3yABb2ZE

# 缓存配置
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Redis配置
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# 日志配置
LOG_CHANNEL=daily
LOG_LEVEL=error
```

### 2. 文件权限设置
```bash
# 设置正确的文件权限
chmod -R 755 /path/to/your/project
chmod -R 777 storage/
chmod -R 777 bootstrap/cache/
```

### 3. Laravel优化命令
```bash
# 清除缓存
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# 生产环境优化
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan optimize
```

## 安全配置

### 1. 删除安装文件
- [ ] 删除 `public/install` 目录
- [ ] 确认 `storage/install/installed.lock` 文件存在

### 2. 隐藏敏感信息
- [ ] 设置 `APP_DEBUG=false`
- [ ] 配置适当的错误页面
- [ ] 隐藏服务器版本信息

### 3. SSL证书配置
- [ ] 配置HTTPS证书
- [ ] 强制HTTPS重定向
- [ ] 更新APP_URL为https://www.shiptobuy.com

## 数据库配置

### 1. 数据库连接
- 主机: 127.0.0.1
- 端口: 3306
- 数据库: shiptobuy_com_gw
- 用户名: shiptobuy_com_gw
- 密码: 3Zjj86Cz3yABb2ZE
- 表前缀: st_ (自动添加)

### 2. 数据库优化
- [ ] 确保MySQL配置适合生产环境
- [ ] 设置适当的连接池大小
- [ ] 配置慢查询日志

## 性能优化

### 1. 缓存配置
- [ ] 使用Redis作为缓存驱动
- [ ] 配置会话存储到Redis
- [ ] 启用OPcache

### 2. 静态资源优化
- [ ] 配置Nginx静态文件缓存
- [ ] 启用Gzip压缩
- [ ] 配置CDN（如需要）

## 监控和日志

### 1. 日志配置
- [ ] 设置日志级别为error
- [ ] 配置日志轮转
- [ ] 监控错误日志

### 2. Laravel Telescope
- [ ] 生产环境建议禁用或限制访问
- [ ] 已禁用标签功能防止数据库增长

## 备份策略

### 1. 数据库备份
- [ ] 配置定期数据库备份
- [ ] 测试备份恢复流程

### 2. 文件备份
- [ ] 备份上传的文件
- [ ] 备份配置文件

## 部署后检查

### 1. 功能测试
- [ ] 前台页面正常访问
- [ ] 后台管理正常登录
- [ ] 用户注册登录功能
- [ ] 产品浏览和搜索
- [ ] 购物车功能
- [ ] 订单创建流程
- [ ] PayPal支付功能
- [ ] 联系客服支付功能
- [ ] 视频上传功能

### 2. 性能测试
- [ ] 页面加载速度
- [ ] 数据库查询性能
- [ ] 缓存命中率

## 已清理的调试代码

### 1. 后端调试代码
- [x] PaypalNewController中的Log::info调试日志
- [x] CheckoutController中的调试日志
- [x] 临时测试路由和方法

### 2. 前端调试代码
- [x] public/vendor/strongadmin/js/util.js中的console.log (已清理4处)
- [x] resources/views/themes/default/layouts/app.blade.php中的console.log (已清理1处)
- [x] 其他JavaScript调试语句

### 3. 保留的合理日志
- [x] OrderRepository中的支付日志（受APP_DEBUG控制）
- [x] 错误处理和异常日志
- [x] PayPal专用日志通道（14天轮转）

## 注意事项

1. **数据库表前缀**: Laravel会自动添加"st_"前缀，不要手动添加
2. **域名配置**: 确保APP_URL设置为https://www.shiptobuy.com
3. **文件上传**: 确保OSS配置正确用于文件存储
4. **邮件配置**: 根据需要配置SMTP设置
5. **定时任务**: 配置Laravel定时任务（如订单关闭等）

## 部署命令参考

```bash
# 1. 上传代码到服务器
# 2. 安装依赖
composer install --no-dev --optimize-autoloader

# 3. 配置环境变量
cp .env.example .env
# 编辑.env文件设置生产环境配置

# 4. 生成应用密钥
php artisan key:generate

# 5. 运行数据库迁移（如果需要）
php artisan migrate --force

# 6. 优化应用
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan optimize

# 7. 设置文件权限
chmod -R 755 .
chmod -R 777 storage/
chmod -R 777 bootstrap/cache/

# 8. 重启服务
# 重启Nginx和PHP-FPM
```
