<?php $__env->startPush('styles'); ?>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<?php echo $__env->make('layouts.includes.breadcrumb', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<!--主体区域-->
<div class="st-main">
    <div class="container">
        <div class="st-main-detail-reviews-header">
            <h4 class="page-header">
                <?php echo e($rows->total()); ?> Ratings
            </h4>
            <p><?php echo app('translator')->get('5 stars'); ?> <?php echo e($rows->total()>0 ? round($data_total['star5']/$rows->total()*100, 2) : 0); ?>%</p>
            <p><?php echo app('translator')->get('4 stars'); ?> <?php echo e($rows->total()>0 ? round($data_total['star4']/$rows->total()*100, 2) : 0); ?>%</p>
            <p><?php echo app('translator')->get('3 stars'); ?> <?php echo e($rows->total()>0 ? round($data_total['star3']/$rows->total()*100, 2) : 0); ?>%</p>
            <p><?php echo app('translator')->get('2 stars'); ?> <?php echo e($rows->total()>0 ? round($data_total['star2']/$rows->total()*100, 2) : 0); ?>%</p>
            <p><?php echo app('translator')->get('1 star'); ?> <?php echo e($rows->total()>0 ? round($data_total['star1']/$rows->total()*100, 2) : 0); ?>%</p>
            <p>
                <a class="btn btn-warning" href="<?php echo e(route('comment.create', ['sku'=>request('sku')])); ?>" target="_parent"><?php echo app('translator')->get('Create New Comment'); ?></a>
            </p>
        </div>
        <div class="st-main-detail-reviews-content">
            <?php $__currentLoopData = $rows; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <dl>
                <dt>
                    <font><?php echo e($row->last_name); ?></font>
                    <span>
                        <?php for($i=1;$i<=$row->star;$i++): ?>
                        <i class="bi-star-fill"></i>
                        <?php endfor; ?>
                    </span>
                </dt>
                <dd class="st-text"><?php echo e($row->content); ?></dd>
                <dd class="st-datetime"><?php echo e($row->created_at); ?></dd>
            </dl>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        <!--分页-->
        <nav aria-label="Page navigation">
            <?php echo e($rows->links()); ?>

        </nav>
    </div>
</div>
<div class="st-h100"></div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('scripts_bottom'); ?>
<script>
    !function () {

    }
    ();
</script>

<?php $__env->stopPush(); ?>
<?php echo $__env->make($layouts, \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\wwwroot\shiptobuy_guanwang\resources\views\themes\default/product/comment/list.blade.php ENDPATH**/ ?>