# Nginx配置示例 - 适用于CentOS 7 + 宝塔面板
# 域名: www.shiptobuy.com

server {
    listen 80;
    server_name www.shiptobuy.com shiptobuy.com;
    
    # 强制HTTPS重定向
    return 301 https://www.shiptobuy.com$request_uri;
}

server {
    listen 443 ssl http2;
    server_name www.shiptobuy.com;
    
    # SSL证书配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers on;
    
    # 网站根目录
    root /www/wwwroot/shiptobuy.com/public;
    index index.php index.html index.htm;
    
    # 字符集
    charset utf-8;
    
    # 日志文件
    access_log /www/wwwroot/shiptobuy.com/storage/logs/nginx-access.log;
    error_log /www/wwwroot/shiptobuy.com/storage/logs/nginx-error.log;
    
    # 安全头部
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # 隐藏Nginx版本
    server_tokens off;
    
    # 文件上传大小限制
    client_max_body_size 100M;
    
    # Laravel路由处理
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # PHP处理
    location ~ \.php$ {
        fastcgi_pass unix:/tmp/php-cgi-72.sock;  # 根据宝塔面板PHP版本调整
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        
        # PHP执行时间
        fastcgi_read_timeout 300;
        fastcgi_send_timeout 300;
    }
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # 禁止访问敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ /\.env {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ /composer\.(json|lock) {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ /package\.json {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 禁止访问安装目录（生产环境应删除）
    location ~ ^/install {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Laravel存储目录
    location ~ ^/storage/(.*)$ {
        alias /www/wwwroot/shiptobuy.com/storage/app/public/$1;
        access_log off;
        expires 1y;
        add_header Cache-Control "public";
    }
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/x-javascript
        application/xml+rss
        application/javascript
        application/json;
    
    # 防止直接访问PHP文件（除了index.php）
    location ~ ^/(app|bootstrap|config|database|resources|routes|storage|tests|vendor)/ {
        deny all;
        access_log off;
        log_not_found off;
    }
}

# 重定向 shiptobuy.com 到 www.shiptobuy.com
server {
    listen 443 ssl http2;
    server_name shiptobuy.com;
    
    # SSL证书配置（与主域名相同）
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    
    return 301 https://www.shiptobuy.com$request_uri;
}
