# PayPal新版支付方式安装说明

## 当前状态
✅ **已完成**：
- PayPal新版支付方式已添加到数据库
- 控制器和视图文件已创建
- 路由已配置
- 管理后台表单已更新

⚠️ **待完成**：
- 安装PayPal Checkout SDK
- 配置PayPal凭据

## 问题说明
PayPal新版支付功能需要安装 `paypal/paypal-checkout-sdk` 包，但当前PHP环境缺少以下扩展：
- openssl
- curl  
- gd
- fileinfo

## 解决方案

### 方案1：通过BT面板安装PHP扩展（推荐）
1. 登录BT面板
2. 进入 "软件商店" → "已安装"
3. 找到PHP 7.2，点击"设置"
4. 在"安装扩展"中安装以下扩展：
   - openssl
   - curl
   - gd
   - fileinfo
5. 重启PHP服务
6. 然后运行：`composer require paypal/paypal-checkout-sdk`

### 方案2：手动安装（如果方案1不可行）
1. 下载PayPal SDK文件到vendor目录
2. 手动配置autoload

### 方案3：暂时使用原版PayPal（临时方案）
如果暂时无法安装SDK，可以继续使用原有的PayPal支付方式。

## 配置步骤（SDK安装完成后）

### 1. 获取PayPal凭据
1. 登录 [PayPal Developer](https://developer.paypal.com/)
2. 创建应用获取Client ID和Client Secret

### 2. 在管理后台配置
1. 进入管理后台 → 支付方式管理
2. 编辑"PayPal (新版)"
3. 填入：
   - Client ID：从PayPal Developer获取
   - Client Secret：从PayPal Developer获取
   - 环境：sandbox（测试）或 live（正式）

### 3. 测试支付流程
1. 在前台选择"PayPal (新版)"支付方式
2. 完成支付测试

## 当前可用的支付方式
1. ✅ **PayPal（原版）** - 正常工作
2. ⚠️ **PayPal（新版）** - 需要安装SDK
3. ✅ **其他支付找客服** - 正常工作

## 技术支持
如需帮助安装PHP扩展或PayPal SDK，请联系技术支持。
