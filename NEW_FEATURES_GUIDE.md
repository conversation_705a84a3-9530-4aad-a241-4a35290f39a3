# 新功能使用指南

## 1. PayPal新版支付 - 连接测试功能

### 功能描述
在PayPal支付页面添加了"测试PayPal连接"按钮，可以检测PayPal配置是否正确，能否正常连接PayPal API。

### 使用方法
1. 进入PayPal支付页面（订单支付时选择PayPal新版支付）
2. 点击"测试PayPal连接"按钮
3. 系统会自动检测：
   - PayPal Client ID 和 Client Secret 是否配置
   - 能否成功获取PayPal访问令牌
   - 显示当前环境（sandbox/live）和部分Client ID

### 测试结果
- ✅ **连接成功**：显示绿色提示，包含环境信息和Client ID前缀
- ❌ **连接失败**：显示红色错误信息，包含具体失败原因

### 技术实现
- **后端接口**：`POST /payment/paypal-new/test-connection`
- **控制器方法**：`PaypalNewController@testConnection`
- **前端实现**：使用Fetch API发送AJAX请求，动态显示结果

### 可能的错误信息
- "PayPal支付方式未配置" - 后台未添加PayPal新版支付方式
- "PayPal Client ID 或 Client Secret 未配置" - 缺少必要的API凭据
- "PayPal连接失败，请检查配置" - API凭据错误或网络问题

---

## 2. 联系客服支付 - 二维码删除功能

### 功能描述
在后台支付方式配置页面，为"联系客服支付"方式的二维码上传功能添加了删除功能，删除时会同时删除数据库记录和服务器上的图片文件。

### 使用方法
1. 登录后台管理系统
2. 进入"支付方式管理"
3. 编辑"其他支付找客服"支付方式
4. 在二维码配置区域：
   - 如果已上传二维码，会显示"删除二维码"按钮
   - 点击删除按钮，确认删除
   - 系统会删除数据库记录和物理文件

### 功能特点
- **完整删除**：同时删除数据库记录和服务器文件
- **安全确认**：删除前会弹出确认对话框
- **实时更新**：删除后立即更新前端显示
- **错误处理**：提供详细的错误提示信息

### 技术实现
- **后端接口**：`POST /strongadmin/payment-option/delete-qr-code`
- **控制器方法**：`PaymentOptionController@deleteQrCode`
- **文件删除**：使用Laravel Storage删除物理文件
- **前端实现**：使用jQuery AJAX和Layui组件

### 删除流程
1. 前端发送删除请求，包含图片路径
2. 后端验证请求参数
3. 删除storage/app/public目录下的图片文件
4. 返回删除结果
5. 前端更新界面显示

---

## 测试步骤

### PayPal连接测试
1. **准备工作**：
   - 确保后台已配置PayPal新版支付方式
   - 设置正确的Client ID和Client Secret
   - 选择正确的环境（sandbox/live）

2. **测试步骤**：
   ```
   1. 创建一个测试订单
   2. 选择PayPal新版支付
   3. 在支付页面点击"测试PayPal连接"
   4. 观察测试结果
   ```

3. **预期结果**：
   - 配置正确：显示绿色成功提示
   - 配置错误：显示红色错误信息

### 二维码删除测试
1. **准备工作**：
   - 登录后台管理系统
   - 确保"联系客服支付"方式已配置
   - 上传一个测试二维码

2. **测试步骤**：
   ```
   1. 进入支付方式管理
   2. 编辑"其他支付找客服"
   3. 确认二维码已显示
   4. 点击"删除二维码"按钮
   5. 确认删除操作
   6. 检查前端显示和文件是否删除
   ```

3. **验证方法**：
   - 前端：二维码预览消失，删除按钮消失
   - 后端：检查storage/app/public目录，确认文件已删除
   - 数据库：more字段中qr_code值应为空

---

## 注意事项

### PayPal连接测试
- 测试功能不会产生实际支付
- 仅验证API连接和配置正确性
- 建议在配置PayPal后立即测试
- 如果测试失败，请检查网络连接和API凭据

### 二维码删除功能
- 删除操作不可逆，请谨慎操作
- 删除会同时移除数据库记录和物理文件
- 建议在删除前备份重要的二维码图片
- 删除后需要重新上传才能恢复

### 安全考虑
- 二维码删除功能仅限后台管理员使用
- 所有操作都有日志记录
- 建议定期备份上传的文件

---

## 故障排除

### PayPal连接测试失败
1. **检查配置**：
   - 验证Client ID和Client Secret是否正确
   - 确认环境设置（sandbox/live）
   - 检查网络连接

2. **常见问题**：
   - 401错误：API凭据错误
   - 网络超时：检查服务器网络连接
   - SSL错误：检查SSL证书配置

### 二维码删除失败
1. **权限问题**：
   - 检查storage目录写权限
   - 确认文件是否存在

2. **路径问题**：
   - 验证图片路径是否正确
   - 检查storage/app/public目录结构

### 联系支持
如遇到问题，请提供：
- 错误信息截图
- 浏览器控制台日志
- 服务器错误日志
- 操作步骤描述
