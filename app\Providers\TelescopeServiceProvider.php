<?php

namespace App\Providers;

use Illuminate\Support\Facades\Gate;
use <PERSON><PERSON>\Telescope\IncomingEntry;
use <PERSON><PERSON>\Telescope\Telescope;
use <PERSON><PERSON>\Telescope\TelescopeApplicationServiceProvider;
use <PERSON><PERSON>\Telescope\EntryType;

class TelescopeServiceProvider extends TelescopeApplicationServiceProvider
{

    public $allowUrlPatterns = ['payment/*'];

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {

        //Telescope::night();

        $this->hideSensitiveRequestDetails();

        // Disable tag functionality to prevent st_telescope_entries_tags table growth
        // This returns an empty array for all entries, preventing any tags from being stored
        Telescope::tag(function (IncomingEntry $entry) {
            return [];
        });

        Telescope::filter(function (IncomingEntry $entry) {
            if (request()->is($this->allowUrlPatterns))
            {
                return true;
            }

            if ($this->app->environment(['local', 'testing', 'staging']))
            {
                return true;
            }

            return $entry->isReportableException() ||
            $entry->isFailedRequest() ||
            $entry->isFailedJob() ||
            $entry->isScheduledTask() ||
            $entry->hasMonitoredTag();
        });
    }

    /**
     * Prevent sensitive request details from being logged by Telescope.
     *
     * @return void
     */
    protected function hideSensitiveRequestDetails()
    {
        if ($this->app->environment(['local', 'testing', 'staging']))
        {
            return;
        }

        Telescope::hideRequestParameters(['_token']);

        Telescope::hideRequestHeaders([
            'cookie',
            'x-csrf-token',
            'x-xsrf-token',
        ]);
    }

    protected function authorization()
    {
        $this->gate();

        Telescope::auth(function ($request) {
            if (app()->environment('local'))
            {
                return true;
            }
            $user = $request->user('strongadmin');
            return in_array($user->user_name, ['admin', 'demo']);
        });
    }

    /**
     * Register the Telescope gate.
     *
     * This gate determines who can access Telescope in non-local environments.
     *
     * @return void
     */
    protected function gate()
    {
        Gate::define('viewTelescope', function ($user) {
            return true;
        });
    }

}
