@extends('layouts.app')

@section('title', 'PayPal支付 - 新版')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4>PayPal支付 - 新版</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>订单信息</h5>
                            <table class="table table-sm">
                                <tr>
                                    <td>订单号:</td>
                                    <td>{{ $order->order_no }}</td>
                                </tr>
                                <tr>
                                    <td>订单金额:</td>
                                    <td>{{ $order->currency_code }} {{ number_format($order->order_amount, 2) }}</td>
                                </tr>
                                <tr>
                                    <td>支付方式:</td>
                                    <td>{{ $payment->title }}</td>
                                </tr>
                            </table>
                            
                            <h5>商品清单</h5>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>商品</th>
                                            <th>数量</th>
                                            <th>单价</th>
                                            <th>小计</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($order->orderProducts as $product)
                                        <tr>
                                            <td>{{ $product->title }}</td>
                                            <td>{{ $product->qty }}</td>
                                            <td>{{ $order->currency_code }} {{ number_format($product->sale_price, 2) }}</td>
                                            <td>{{ $order->currency_code }} {{ number_format($product->subtotal, 2) }}</td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h5>PayPal支付</h5>
                            <div id="paypal-button-container"></div>
                            <div id="payment-message" class="mt-3"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- PayPal JavaScript SDK -->
<script src="https://www.paypal.com/sdk/js?client-id={{ $client_id }}&currency={{ $order->currency_code }}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {

    // 渲染PayPal按钮
    paypal.Buttons({
        // 创建订单
        createOrder: function(data, actions) {
            return fetch('{{ route("paypal_new.create_order") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    orderId: {{ $order->id }}
                })
            })
            .then(function(response) {
                return response.json();
            })
            .then(function(orderData) {
                if (orderData.error) {
                    throw new Error(orderData.error);
                }
                return orderData.id;
            })
            .catch(function(error) {
                console.error('创建PayPal订单失败:', error);
                showMessage('创建支付订单失败: ' + error.message, 'error');
                throw error;
            });
        },

        // 支付批准后
        onApprove: function(data, actions) {
            showMessage('正在处理支付...', 'info');
            
            // 跳转到成功页面，让服务器端处理捕获
            window.location.href = '{{ route("paypal_new.success") }}?token=' + data.orderID;
        },

        // 支付取消
        onCancel: function(data) {
            showMessage('支付已取消', 'warning');
            setTimeout(function() {
                window.location.href = '{{ route("paypal_new.cancel") }}';
            }, 2000);
        },

        // 支付错误
        onError: function(err) {
            console.error('PayPal支付错误:', err);
            showMessage('支付过程中发生错误，请重试', 'error');
        }
    }).render('#paypal-button-container');

    // 显示消息的函数
    function showMessage(message, type) {
        const messageDiv = document.getElementById('payment-message');
        let alertClass = 'alert-info';
        
        switch(type) {
            case 'success':
                alertClass = 'alert-success';
                break;
            case 'error':
                alertClass = 'alert-danger';
                break;
            case 'warning':
                alertClass = 'alert-warning';
                break;
        }
        
        messageDiv.innerHTML = '<div class="alert ' + alertClass + '">' + message + '</div>';
        
        // 3秒后自动清除消息（除了错误消息）
        if (type !== 'error') {
            setTimeout(function() {
                messageDiv.innerHTML = '';
            }, 3000);
        }
    }
});
</script>
@endpush

@push('styles')
<style>
.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.table-sm td, .table-sm th {
    padding: 0.3rem;
}

#paypal-button-container {
    margin-top: 20px;
}

.alert {
    margin-bottom: 0;
    padding: 10px 15px;
    border-radius: 4px;
}
</style>
@endpush
