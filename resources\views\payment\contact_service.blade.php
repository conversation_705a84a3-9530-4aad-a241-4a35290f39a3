@extends('layouts.app')

@section('title', '联系客服支付')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header">
                    <h4>{{ $payment->title }}</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 订单信息 -->
                        <div class="col-md-6">
                            <h5>订单信息</h5>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>订单号:</strong></td>
                                    <td>{{ $order->order_no }}</td>
                                </tr>
                                <tr>
                                    <td><strong>订单金额:</strong></td>
                                    <td class="text-danger">
                                        <strong>{{ $order->currency_code }} {{ number_format($order->order_amount, 2) }}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>支付方式:</strong></td>
                                    <td>{{ $payment->title }}</td>
                                </tr>
                                <tr>
                                    <td><strong>下单时间:</strong></td>
                                    <td>{{ $order->created_at->format('Y-m-d H:i:s') }}</td>
                                </tr>
                            </table>
                            
                            <h5>商品清单</h5>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>商品名称</th>
                                            <th>数量</th>
                                            <th>单价</th>
                                            <th>小计</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($order->orderProducts as $product)
                                        <tr>
                                            <td>{{ $product->title }}</td>
                                            <td>{{ $product->qty }}</td>
                                            <td>{{ $order->currency_code }} {{ number_format($product->sale_price, 2) }}</td>
                                            <td>{{ $order->currency_code }} {{ number_format($product->subtotal, 2) }}</td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <!-- 支付说明和二维码 -->
                        <div class="col-md-6">
                            <h5>支付说明</h5>
                            
                            @if(!empty($payment->more['description']))
                            <div class="alert alert-info">
                                {!! nl2br(e($payment->more['description'])) !!}
                            </div>
                            @endif
                            
                            @if(!empty($payment->more['qr_code']))
                            <div class="text-center mb-4">
                                <h6>扫描二维码联系客服</h6>
                                <img src="{{ asset('storage/' . $payment->more['qr_code']) }}" 
                                     alt="客服二维码" 
                                     class="img-fluid qr-code-image"
                                     style="max-width: 200px; border: 1px solid #ddd; padding: 10px;">
                                <p class="text-muted mt-2">
                                    <small>请扫描上方二维码添加客服微信</small>
                                </p>
                            </div>
                            @endif
                            
                            <div class="payment-instructions">
                                <h6>支付流程：</h6>
                                <ol>
                                    <li>扫描上方二维码添加客服微信</li>
                                    <li>向客服发送您的订单号：<strong>{{ $order->order_no }}</strong></li>
                                    <li>客服将为您提供其他支付方式选项</li>
                                    <li>完成支付后，客服将为您确认订单状态</li>
                                </ol>
                            </div>
                            
                            <div class="alert alert-warning">
                                <strong>注意事项：</strong>
                                <ul class="mb-0">
                                    <li>请保存好您的订单号以便客服查询</li>
                                    <li>客服工作时间：周一至周日 9:00-21:00</li>
                                    <li>如遇客服繁忙，请耐心等待回复</li>
                                </ul>
                            </div>
                            
                            <div class="text-center">
                                <button type="button" class="btn btn-primary btn-lg" id="confirmContactBtn">
                                    <i class="fas fa-check"></i> 我已了解，确认使用此支付方式
                                </button>
                            </div>
                            
                            <div id="result-message" class="mt-3"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- SweetAlert2 CDN -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const confirmBtn = document.getElementById('confirmContactBtn');
    const resultDiv = document.getElementById('result-message');
    
    confirmBtn.addEventListener('click', function() {
        // 禁用按钮防止重复点击
        confirmBtn.disabled = true;
        confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
        
        // 发送确认请求
        fetch('{{ route("contact_service.confirm") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                orderId: {{ $order->id }}
            })
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);

            if (!response.ok) {
                throw new Error('HTTP ' + response.status + ': ' + response.statusText);
            }

            // 检查响应内容类型
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                return response.text().then(text => {
                    console.error('Expected JSON but got:', text);
                    throw new Error('服务器返回了非JSON响应: ' + text.substring(0, 100));
                });
            }

            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            if (data.success) {
                // 显示成功消息
                resultDiv.innerHTML = '<div class="alert alert-success">' + data.message + '</div>';
                confirmBtn.innerHTML = '<i class="fas fa-check"></i> 已确认';
                confirmBtn.classList.remove('btn-primary');
                confirmBtn.classList.add('btn-success');

                // 弹窗提示并跳转（类似PayPal支付成功）
                setTimeout(function() {
                    // 检查是否有SweetAlert2，如果没有则使用原生alert
                    if (typeof Swal !== 'undefined') {
                        Swal.fire({
                            icon: 'success',
                            title: '支付方式确认成功！',
                            text: data.message,
                            confirmButtonText: '查看订单详情',
                            allowOutsideClick: false,
                            allowEscapeKey: false
                        }).then((result) => {
                            if (data.redirect_url) {
                                window.location.href = data.redirect_url;
                            }
                        });
                    } else {
                        // 使用原生alert作为后备方案
                        alert('支付方式确认成功！\n\n' + data.message + '\n\n即将跳转到订单详情页面...');
                        if (data.redirect_url) {
                            window.location.href = data.redirect_url;
                        }
                    }
                }, 1000);
            } else {
                throw new Error(data.error || '操作失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            resultDiv.innerHTML = '<div class="alert alert-danger">操作失败：' + error.message + '</div>';
            confirmBtn.disabled = false;
            confirmBtn.innerHTML = '<i class="fas fa-check"></i> 我已了解，确认使用此支付方式';
        });
    });
});
</script>
@endpush

@push('styles')
<style>
.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.table-sm td, .table-sm th {
    padding: 0.3rem;
}

.qr-code-image {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.payment-instructions ol {
    padding-left: 1.2rem;
}

.payment-instructions li {
    margin-bottom: 0.5rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
}

.alert ul {
    padding-left: 1.2rem;
}

.alert li {
    margin-bottom: 0.25rem;
}
</style>
@endpush
