<?php $__env->startPush('styles'); ?>
<meta http-equiv="pragram" content="no-cache">
<meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate">
<style>
    .carousel-indicators li{
        border: 1px solid gray;
    }
    .carousel-indicators .active{
        border: 1px solid #fff;
        background: gray;
    }
</style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
<!-- Banner 轮播图-->
<div class="st-banner">
    <div class="container">
        <div id="carousel-example-generic" class="carousel slide" data-ride="carousel">
            <ol class="carousel-indicators">
                <li data-target="#carousel-example-generic" data-slide-to="0" class="active"></li>
                <li data-target="#carousel-example-generic" data-slide-to="1" class=""></li>
            </ol>
            <div class="carousel-inner" role="listbox">
                <div class="item active">
                    <a href="https://www.shiptobuy.com/product-50.html"><img alt="<?php echo e(config('strongshop.storeName')); ?>" src="img/banner01.jpg" data-holder-rendered="true"></a>
                </div>
                <div class="item">
                    <a href="#"><img alt="<?php echo e(config('strongshop.storeName')); ?>" src="img/banner02.jpg" data-holder-rendered="true"></a>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="st-h20"></div>

<div class="st-home-product">
<script>
// 动态插入优化版箭头
document.addEventListener('DOMContentLoaded', function(){
    const carousel = document.getElementById('carousel-example-generic');
    
    // 创建箭头函数
    const createArrow = (side) => {
        const arrow = document.createElement('div');
        arrow.className = `carousel-arrow arrow-${side}`;
        arrow.onclick = () => {
            $(carousel).carousel(side === 'left' ? 'prev' : 'next');
        };
        return arrow;
    };

    // 插入箭头
    [createArrow('left'), createArrow('right')].forEach(arrow => {
        arrow.style.top = `${carousel.offsetHeight / 2}px`; // 动态计算垂直位置
        carousel.appendChild(arrow);
    });

    // 窗口大小变化时重新定位
    window.addEventListener('resize', () => {
        [...document.getElementsByClassName('carousel-arrow')].forEach(arrow => {
            arrow.style.top = `${carousel.offsetHeight / 2}px`;
        });
    });
});
</script>
    <!--推荐产品-->
    <?php if($recommendRows->isNotEmpty()): ?>
    <div class="container">
        <div class="page-header">
            <h4><a href="<?php echo e(route('product.list', ['is_recommend'=>1])); ?>" class="st-home-product-title"><span class="glyphicon glyphicon-chevron-right"></span> <?php echo app('translator')->get('Recommend Products'); ?></a></h4>
        </div>
        <div class="row">
            <?php $__currentLoopData = $recommendRows; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $recommendRow): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-xs-6 col-sm-4 col-md-3 col-lg-2">
                <div class="thumbnail">
                    <a href="<?php echo e(route('product.show.rewrite', ['id'=>$recommendRow->id])); ?>" class="st-thumb">
                        <img alt="<?php echo e($recommendRow->title); ?>" src="<?php echo e($recommendRow->img_cover); ?>" class="img-responsive" />
                    </a>
                    <div class="caption">
                        <h5 title="<?php echo e($recommendRow->title); ?>"><a href="<?php echo e(route('product.show.rewrite', ['id'=>$recommendRow->id])); ?>"><?php echo e($recommendRow->title); ?></a></h5>
                        <p class="st-home-product-price"><?php echo \App\Repositories\AppRepository::getCurrentCurrencyName() . ' ' . $recommendRow->sale_price; ?></p>
                         <script>
    document.addEventListener("DOMContentLoaded", function(){
        document.querySelectorAll('.st-home-product-price').forEach(e => {
            e.textContent = e.textContent.replace(/(\d+\.\d{3})/, m => (+m).toFixed(2));
        });
    });
  </script>
                        <p class="st-home-product-sold"><?php echo e($recommendRow->click_num); ?> <?php echo app('translator')->get('clicks'); ?></p>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
    <?php endif; ?>
    <!--新品-->
    <?php if($newRows->isNotEmpty()): ?>
    <div class="container">
        <div class="page-header">
            <h4><a href="<?php echo e(route('product.list', ['is_new'=>1])); ?>" class="st-home-product-title"><span class="glyphicon glyphicon-chevron-right"></span> <?php echo app('translator')->get('New Products'); ?></a></h4>
        </div>
        <div class="row">
            <?php $__currentLoopData = $newRows; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $newRow): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-xs-6 col-sm-4 col-md-3 col-lg-2">
                <div class="thumbnail">
                    <a href="<?php echo e(route('product.show.rewrite', ['id'=>$newRow->id])); ?>" class="st-thumb">
                        <img alt="<?php echo e($newRow->title); ?>" src="<?php echo e($newRow->img_cover); ?>" class="img-responsive" />
                    </a>
                    <div class="caption">
                        <h5 title="<?php echo e($newRow->title); ?>"><a href="<?php echo e(route('product.show.rewrite', ['id'=>$newRow->id])); ?>"><?php echo e($newRow->title); ?></a></h5>
                        <p class="st-home-product-price"><?php echo \App\Repositories\AppRepository::getCurrentCurrencyName() . ' ' . $newRow->sale_price; ?></p>
                        <p class="st-home-product-sold"><?php echo e($newRow->click_num); ?> <?php echo app('translator')->get('clicks'); ?></p>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
    <?php endif; ?>
    <!--热卖-->
    <?php if($hotRows->isNotEmpty()): ?>
    <div class="container">
        <div class="page-header">
            <h4><a href="<?php echo e(route('product.list', ['is_hot'=>1])); ?>" class="st-home-product-title"><span class="glyphicon glyphicon-chevron-right"></span> <?php echo app('translator')->get('Hot Products'); ?></a></h4>
        </div>
        <div class="row">
            <?php $__currentLoopData = $hotRows; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $hotRow): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-xs-6 col-sm-4 col-md-3 col-lg-2">
                <div class="thumbnail">
                    <a href="<?php echo e(route('product.show.rewrite', ['id'=>$hotRow->id])); ?>" class="st-thumb">
                        <img alt="<?php echo e($hotRow->title); ?>" src="<?php echo e($hotRow->img_cover); ?>" class="img-responsive" />
                    </a>
                    <div class="caption">
                        <h5 title="<?php echo e($hotRow->title); ?>"><a href="<?php echo e(route('product.show.rewrite', ['id'=>$hotRow->id])); ?>"><?php echo e($hotRow->title); ?></a></h5>
                        <p class="st-home-product-price"><?php echo \App\Repositories\AppRepository::getCurrentCurrencyName() . ' ' . $hotRow->sale_price; ?></p>
                        <p class="st-home-product-sold"><?php echo e($hotRow->click_num); ?> <?php echo app('translator')->get('clicks'); ?></p>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\wwwroot\shiptobuy_guanwang\resources\views\themes\default/home.blade.php ENDPATH**/ ?>