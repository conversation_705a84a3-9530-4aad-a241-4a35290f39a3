{"name": "openstrong/strongshop", "type": "project", "description": "StrongShop 是一款免费开源的跨境电商商城网站。基于 PHP Laravel6 框架开发，遵循 BSD-3-Clause 开源协议，免费商用。支持多语言，多货币，多种国际配送方式。PayPal 支付，国际信用卡支付。PC Web 端和移动端自适应。", "homepage": "http://www.strongshop.cn", "keywords": ["framework", "laravel", "strongshop", "shop"], "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.strongshop.cn"}], "require": {"php": "^7.2.5|^8.0", "alibabacloud/dm-20151123": "1.0.0", "fideloper/proxy": "^4.4", "iidestiny/laravel-filesystem-oss": "^2.1", "laravel-lang/lang": "~6.1", "laravel/framework": "^6.20", "laravel/socialite": "^5.5", "laravel/telescope": "^3.2", "laravel/tinker": "^2.5", "mews/captcha": "^3.2", "openstrong/laravel-strongadmin": "^2.0", "overtrue/laravel-filesystem-qiniu": "^1.0", "paypal/rest-api-sdk-php": "^1.14", "paypal/paypal-checkout-sdk": "^1.0", "tamayo/laravel-scout-elastic": "^5.0"}, "require-dev": {"facade/ignition": "^1.16.4", "fakerphp/faker": "^1.9.1", "laravel/ui": "^1.0", "mockery/mockery": "^1.0", "nunomaduro/collision": "^3.0", "phpunit/phpunit": "^8.5.8|^9.3.3"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/"}, "classmap": ["database/seeds", "database/factories"], "files": ["app/Helpers/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}