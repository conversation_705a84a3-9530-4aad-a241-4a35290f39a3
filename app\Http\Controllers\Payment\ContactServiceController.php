<?php

/**
 * StrongShop
 * <AUTHOR> <<EMAIL>>
 * @license http://www.strongshop.cn/license/
 * @copyright StrongShop Software
 */

namespace App\Http\Controllers\Payment;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Order\Order;
use App\Models\PaymentOption;
use App\Repositories\OrderRepository;

class ContactServiceController extends Controller
{
    /**
     * 联系客服支付页面
     */
    public function pay(Request $request)
    {
        $orderId = $request->orderId;
        
        // 获取订单信息
        $order = Order::query()->where('id', $orderId)
                ->with('orderProducts')
                ->first();
                
        if (!$order) {
            abort(404);
        }
        
        if (!in_array($order->order_status, [Order::STATUS_UNPAID, Order::STATUS_PAY_FAILED])) {
            abort(201, __('Invalid'));
        }

        // 获取支付方式配置
        $model = PaymentOption::where('code', 'contact_service')->where('status', 1)->first();
        if (!$model) {
            abort(201, __('Payment not exists'));
        }

        return view('payment.contact_service', [
            'order' => $order,
            'payment' => $model
        ]);
    }

    /**
     * 确认联系客服
     */
    public function confirm(Request $request)
    {
        $orderId = $request->orderId;
        
        // 获取订单信息
        $order = Order::query()->where('id', $orderId)->first();
        
        if (!$order) {
            return response()->json(['error' => '订单不存在'], 404);
        }
        
        if (!in_array($order->order_status, [Order::STATUS_UNPAID, Order::STATUS_PAY_FAILED])) {
            return response()->json(['error' => '订单状态无效'], 400);
        }

        // 获取支付方式配置
        $model = PaymentOption::where('code', 'contact_service')->where('status', 1)->first();
        if (!$model) {
            return response()->json(['error' => '支付方式不存在'], 400);
        }

        try {
            // 更新订单状态为待支付（保持原状态，等待客服确认）
            $order->payment_option_id = $model->id;
            $order->pay_remark = '客户选择联系客服支付方式，等待客服确认支付 - ' . now()->format('Y-m-d H:i:s');
            $order->save();

            return response()->json([
                'success' => true,
                'message' => '已记录您的支付意向，客服将尽快与您联系确认支付事宜！',
                'redirect_url' => route('user.my.order.detail', ['orderId' => $order->id])
            ]);

        } catch (\Exception $e) {
            \Log::error('联系客服支付确认失败: ' . $e->getMessage(), [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => '操作失败，请重试: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 支付说明页面
     */
    public function instructions(Request $request)
    {
        $orderId = $request->orderId;
        
        // 获取订单信息
        $order = Order::query()->where('id', $orderId)->first();
        
        if (!$order) {
            abort(404);
        }

        // 获取支付方式配置
        $model = PaymentOption::where('code', 'contact_service')->where('status', 1)->first();
        if (!$model) {
            abort(201, __('Payment not exists'));
        }

        return view('payment.contact_service_instructions', [
            'order' => $order,
            'payment' => $model
        ]);
    }
}
