# 配送方式功能更新说明

## 更新内容

为了满足国际物流的需求，我们对配送方式功能进行了以下更新：

### 1. 新增免运费阈值功能

- **功能描述**: 可以设置一个重量阈值，当商品重量达到或超过此阈值时，自动免收运费
- **使用场景**: 适用于"大于等于22公斤免运费"这类需求
- **配置位置**: 后台 -> 网站设置 -> 配送方式 -> 配送区域 -> 免运费设置

#### 配置选项：
- **启用免运费**: 开关控制是否启用此功能
- **免运费重量阈值**: 设置重量阈值（单位：克）
  - 例如：设置22000表示22公斤以上免运费

### 2. 支持续重费用设置为0

- **功能描述**: 续重的重量和费用都可以设置为0，实现只收取首重费用的效果
- **使用场景**: 适用于国际物流按首重起步的计费方式
- **配置方式**: 在配送公式中将续重的重量和费用都设置为0

#### 示例配置：
```
重量范围：0-21999克
首重：430USD / 500克
续重：0USD / 0克  ← 设置为0表示不收续重费
```

### 3. 超出范围计费灵活配置

- **功能描述**: 超出重量范围的计费可以启用/禁用，费用也可以设置为0
- **使用场景**:
  - 禁用超出范围计费：超出范围后不再额外收费
  - 设置为0：超出范围后费用为0
- **配置位置**: 后台 -> 网站设置 -> 配送方式 -> 配送区域 -> 超出范围计费设置

#### 配置选项：
- **启用超出范围计费**: 开关控制是否对超出范围的重量收费
- **超出范围费用**: 可以设置为0或任意数值
- **超出范围重量**: 可以设置为0或任意数值

## 修改的文件

### 1. 后端文件

#### `app/Models/ShippingOptionConfig.php`
- 修改验证规则，允许续重费用为0 (`gte:0` 替代 `gt:0`)
- 修改验证规则，允许超出范围费用为0 (`gte:0` 替代 `gt:0`)
- 添加免运费阈值字段的验证规则和标签
- 添加超出范围启用字段的验证规则和标签

#### `app/Repositories/ShippingRepository.php`
- 修改 `getFee()` 方法，添加免运费阈值检查逻辑
- 优化续重费用计算，支持续重为0的情况
- 添加超出范围计费的启用/禁用检查逻辑

#### `app/Http/Controllers/Strongadmin/ShippingOptionConfigController.php`
- 在创建和更新方法中添加免运费阈值数据处理逻辑
- 在创建和更新方法中添加超出范围启用字段数据处理逻辑
- 确保免运费和超出范围开关状态正确保存

### 2. 前端文件

#### `resources/views/strongadmin/shippingOptionConfig/form.blade.php`
- 添加免运费设置区域
- 添加超出范围计费设置区域
- 修改续重费用表单，添加提示说明可以设置为0
- 修改超出范围费用表单，添加启用/禁用开关和提示说明
- 优化表单布局和用户体验

## 使用示例

### 场景1：国际物流首重计费
```
免运费设置：
- 启用免运费：开启
- 免运费重量阈值：22000克（22公斤）

配送公式：
- 重量范围：0-21999克
- 首重：430USD / 500克
- 续重：0USD / 0克
```

**计费效果**：
- 重量 < 22公斤：只收430USD首重费
- 重量 ≥ 22公斤：免运费

### 场景2：禁用超出范围计费
```
免运费设置：
- 启用免运费：关闭

配送公式：
- 重量范围：0-10000克（10公斤）
- 首重：430USD / 500克
- 续重：0USD / 0克

超出范围计费设置：
- 启用超出范围计费：关闭
```

**计费效果**：
- 重量 ≤ 10公斤：收取430USD首重费
- 重量 > 10公斤：仍然只收取430USD首重费（不额外收费）

### 场景3：传统阶梯计费
```
免运费设置：
- 启用免运费：关闭

配送公式：
- 重量范围：0-21999克
- 首重：430USD / 500克
- 续重：1USD / 1克

超出范围计费设置：
- 启用超出范围计费：开启
- 超出范围：2USD / 1克
```

**计费效果**：
- 按首重+续重+超出范围正常计算费用

## 注意事项

1. **重量单位**: 系统中所有重量都以克为单位
2. **免运费优先级**: 当启用免运费阈值且重量达到阈值时，直接返回0费用，不再计算其他费用
3. **续重为0**: 当续重重量或费用设置为0时，系统只收取首重费用
4. **超出范围灵活性**: 可以禁用超出范围计费，或将费用设置为0
5. **向下兼容**: 现有配置不受影响，新功能为可选功能，超出范围计费默认启用

## 测试建议

建议在正式使用前进行以下测试：

1. 测试免运费阈值功能是否正常工作
2. 测试续重为0的情况下费用计算是否正确
3. 测试超出范围计费启用/禁用功能
4. 测试超出范围费用设置为0的情况
5. 测试不同重量区间的费用计算
6. 验证前端表单数据保存是否正确
