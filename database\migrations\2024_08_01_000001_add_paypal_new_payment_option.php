<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class AddPaypalNewPaymentOption extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // 添加新的PayPal支付方式（使用最新PayPal API）
        DB::table('payment_option')->insert([
            'title' => 'PayPal (新版)',
            'desc' => '使用最新PayPal API的支付方式，支持更好的用户体验',
            'code' => 'paypal_new',
            'status' => 1,
            'more' => json_encode([
                'env' => 'sandbox',
                'rate' => null,
                'currency' => null,
                'client_id' => 'your_paypal_client_id_here',
                'client_secret' => 'your_paypal_client_secret_here'
            ]),
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // 添加联系客服支付方式
        DB::table('payment_option')->insert([
            'title' => '其他支付找客服',
            'desc' => '联系客服获取更多支付方式选项，支持微信、支付宝等多种支付方式',
            'code' => 'contact_service',
            'status' => 1,
            'more' => json_encode([
                'rate' => null,
                'currency' => null,
                'qr_code' => '', // 客服二维码图片路径
                'description' => "如需使用其他支付方式，请扫描下方二维码联系客服。\n\n我们支持：\n• 微信支付\n• 支付宝\n• 银行转账\n• 其他支付方式\n\n客服将为您提供详细的支付指导。"
            ]),
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // 删除新的PayPal支付方式
        DB::table('payment_option')->where('code', 'paypal_new')->delete();

        // 删除联系客服支付方式
        DB::table('payment_option')->where('code', 'contact_service')->delete();
    }
}
