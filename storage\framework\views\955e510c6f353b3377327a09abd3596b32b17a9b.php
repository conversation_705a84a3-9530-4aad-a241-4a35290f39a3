<?php $__empty_1 = true; $__currentLoopData = $shipping_options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $shipping_option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
<div class="form-group">
    <label>
        <div class="radio">
            <label>
                <input type="radio" name="shipping_option_id" value="<?php echo e($shipping_option['id']); ?>" <?php if($loop->first): ?>checked <?php endif; ?> > <?php echo e($shipping_option['title']); ?> 
                <br/>
                <strong class="badge"><?php echo e($_current_currency_name); ?> <span class="st-shipping-option-fee"><?php echo e($shipping_option['fee']); ?></span></strong>
            </label>
        </div>
        <?php echo e($shipping_option['desc']); ?>

    </label>
</div>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
<p>
    <?php echo app('translator')->get('No shipping options. <a href=":href">Feedback us </a>', ['href'=>route('user.my.feedback')]); ?>
</p>
<?php endif; ?><?php /**PATH D:\wwwroot\shiptobuy_guanwang\resources\views\themes\default/layouts/includes/shippingOptions.blade.php ENDPATH**/ ?>