<?php $__env->startPush('styles'); ?>
<style></style>
<?php $__env->stopPush(); ?>
<?php $__env->startPush('scripts'); ?>
<script></script>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
<div class="st-h15"></div>
<form class="layui-form st-form-search" lay-filter="ST-FORM-SEARCH">
    <div class="layui-form-item"><div class="layui-inline">
            <label class="layui-form-label"><?php echo e($model->getAttributeLabel('title')); ?></label>
            <div class="layui-input-inline">
                <input type="text" name="title" autocomplete="off" placeholder="" class="layui-input">
            </div>
        </div><div class="layui-inline">
            <label class="layui-form-label"><?php echo e($model->getAttributeLabel('shipping_option_id')); ?></label>
            <div class="layui-input-inline">
                <input type="text" name="shipping_option_id" autocomplete="off" placeholder="" class="layui-input">
            </div>
        </div>
        <!-- <div class="layui-inline">
            <label class="layui-form-label"><?php echo e($model->getAttributeLabel('countries')); ?></label>
            <div class="layui-input-inline">
                <input type="text" name="countries" autocomplete="off" placeholder="" class="layui-input">
            </div>
        </div> -->
        <!-- <div class="layui-inline">
            <label class="layui-form-label"><?php echo e($model->getAttributeLabel('states')); ?></label>
            <div class="layui-input-inline">
                <input type="text" name="states" autocomplete="off" placeholder="" class="layui-input">
            </div>
        </div> -->
        <div class="layui-inline">
            <label class="layui-form-label"><?php echo e($model->getAttributeLabel('created_at')); ?></label>
            <div class="layui-input-inline">
                <input type="text" name="created_at_begin" id="date" placeholder="年-月-日" autocomplete="off" class="layui-input">
            </div>
            <div class="layui-input-inline">
                <input type="text" name="created_at_end" id="date2" placeholder="年-月-日" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-inline">
            <a class="layui-btn layui-btn-xs st-search-button">开始搜索</a>
        </div>
    </div>
</form>
<table class="layui-hide" id="ST-TABLE-LIST" lay-filter="ST-TABLE-LIST"></table>
<script type="text/html" id="ST-TOOL-BAR">
    <div class="layui-btn-container st-tool-bar">
        <a class="layui-btn layui-btn-xs" onclick="Util.createFormWindow('/strongadmin/shippingOptionConfig/create?shipping_option_id=<?php echo e(request('shipping_option_id')); ?>&create=1', this.innerText);">添加</a>
        <a class="layui-btn layui-btn-xs" lay-event="batchDelete" data-href="/strongadmin/shippingOptionConfig/destroy">删除选中</a>
    </div>
</script>
<script type="text/html" id="ST-OP-BUTTON">
    
    <a class="layui-btn layui-btn-xs" onclick="Util.createFormWindow('/strongadmin/shippingOptionConfig/update?id={{d.id}}', this.innerText);">更新</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" onclick="Util.destroy('/strongadmin/shippingOptionConfig/destroy?id={{d.id}}');">删除</a>
    
</script>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('scripts_bottom'); ?>        
<script>
!function () {
    //日期
    layui.laydate.render({
        elem: '#date'
    });
    layui.laydate.render({
        elem: '#date2'
    });
    //表格字段
    var cols = [
                {type: 'checkbox', fixed: 'left'}
                , {field: 'id', title: 'id', width: 60, fixed: 'left', unresize: true, totalRowText: '合计', sort: true}
                , {field: 'title', title: '<?php echo e($model->getAttributeLabel("title")); ?>', width: 350, sort: true}
                , {field: 'countries', title: '<?php echo e($model->getAttributeLabel("countries")); ?>', width: 150, sort: true}
                , {field: 'states', title: '<?php echo e($model->getAttributeLabel("states")); ?>', width: 150, sort: true}
                , {field: 'status', title: '<?php echo e($model->getAttributeLabel("status")); ?>', width: 80, sort: true, templet: function (res) {
                    return  res.status==1  ? '<span class="layui-badge layui-bg-green">启用</span>' : '<span class="layui-badge">禁用</span>';
                    }}
                , {field: 'created_at', title: '<?php echo e($model->getAttributeLabel("created_at")); ?>', width: 150, sort: true}
                , {field: 'updated_at', title: '<?php echo e($model->getAttributeLabel("updated_at")); ?>', width: 150, sort: true}
                , {fixed: 'right', title: '操作', toolbar: '#ST-OP-BUTTON', width: 150}
            ];
    var shipping_option_id = "<?php echo e(request('shipping_option_id')); ?>";
    var tableConfig = {
        cols: [cols]
        ,limit: 20 //每页条数
        ,where:{shipping_option_id:shipping_option_id}
    };
    Util.renderTable(tableConfig);
}();
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('strongadmin::layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\wwwroot\shiptobuy_guanwang\resources\views/strongadmin/shippingOptionConfig/index.blade.php ENDPATH**/ ?>