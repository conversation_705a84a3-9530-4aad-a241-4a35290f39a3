@extends('strongadmin::layouts.app')

@push('styles')
<style></style>
@endpush

@push('scripts')
<script></script>
@endpush

@section('content')
<div class="st-h15"></div>
<form class="layui-form" action="">
    <input name="id" type="hidden" value="{{$model->id}}" />
    <div class="layui-row">
        <div class="layui-col-xs11">
            <div class="layui-form-item">
                <label class="layui-form-label st-form-input-required">{{$model->getAttributeLabel('title')}}</label>
                <div class="layui-input-block">
                    <input type="text" name="title" value="{{$model->title}}" autocomplete="off" placeholder="" class="layui-input">
                    <div class="layui-word-aux st-form-tip"></div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">{{$model->getAttributeLabel('desc')}}</label>
                <div class="layui-input-block">
                    <input type="text" name="desc" value="{{$model->desc}}" autocomplete="off" placeholder="" class="layui-input">
                    <div class="layui-word-aux st-form-tip"></div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label st-form-input-required">{{$model->getAttributeLabel('code')}}</label>
                <div class="layui-input-block">
                    <input type="text" name="code" value="{{$model->code}}" autocomplete="off" placeholder="" class="layui-input">
                    <div class="layui-word-aux st-form-tip"></div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label st-form-input-required">{{$model->getAttributeLabel('status')}}</label>
                <div class="layui-input-block">
                    <input type="radio" name="status" value="1" title="启用" @if($model->status==1)checked @endif>
                    <input type="radio" name="status" value="2" title="禁用" @if($model->status==2)checked @endif>
                    <div class="layui-word-aux st-form-tip"></div>
                </div>
            </div>
            <!-- 原版PayPal配置 -->
            <div class="layui-form-item" id="paypal-business" style="display: none;">
                <label class="layui-form-label st-form-input-required">收款账号</label>
                <div class="layui-input-block">
                    <input type="text" name="more[business]" value="{{$model->more['business'] ?? ''}}" autocomplete="off" placeholder="" class="layui-input">
                    <div class="layui-word-aux st-form-tip layui-show">沙盒测试环境账号：<EMAIL></div>
                </div>
            </div>

            <!-- 新版PayPal配置 -->
            <div class="layui-form-item" id="paypal-client-id" style="display: none;">
                <label class="layui-form-label st-form-input-required">Client ID</label>
                <div class="layui-input-block">
                    <input type="text" name="more[client_id]" value="{{$model->more['client_id'] ?? ''}}" autocomplete="off" placeholder="" class="layui-input">
                    <div class="layui-word-aux st-form-tip layui-show">PayPal应用的Client ID</div>
                </div>
            </div>

            <div class="layui-form-item" id="paypal-client-secret" style="display: none;">
                <label class="layui-form-label st-form-input-required">Client Secret</label>
                <div class="layui-input-block">
                    <input type="text" name="more[client_secret]" value="{{$model->more['client_secret'] ?? ''}}" autocomplete="off" placeholder="" class="layui-input">
                    <div class="layui-word-aux st-form-tip layui-show">PayPal应用的Client Secret</div>
                </div>
            </div>

            <!-- PayPal连接测试按钮 -->
            <div class="layui-form-item" id="paypal-test-connection" style="display: none;">
                <label class="layui-form-label">连接测试</label>
                <div class="layui-input-block">
                    <button type="button" class="layui-btn layui-btn-normal" id="test-paypal-btn">
                        <i class="layui-icon layui-icon-link"></i> 测试PayPal连接
                    </button>
                    <div class="layui-word-aux st-form-tip layui-show">点击测试PayPal API配置是否正确</div>
                    <div id="paypal-test-result" style="margin-top: 10px;"></div>
                </div>
            </div>

            <!-- 联系客服支付方式配置 -->
            <div class="layui-form-item" id="contact-service-qr" style="display: none;">
                <label class="layui-form-label">客服二维码</label>
                <div class="layui-input-block">
                    <div class="layui-upload">
                        <button type="button" class="layui-btn" id="qr-upload">上传二维码</button>
                        @if(!empty($model->more['qr_code']))
                        <button type="button" class="layui-btn layui-btn-danger" id="qr-delete" style="margin-left: 10px;">删除二维码</button>
                        @endif
                        <div class="layui-upload-list">
                            @if(!empty($model->more['qr_code']))
                            <img class="layui-upload-img" id="qr-preview" src="{{ asset('storage/' . $model->more['qr_code']) }}" style="width: 100px; height: 100px;">
                            @else
                            <img class="layui-upload-img" id="qr-preview" style="width: 100px; height: 100px; display: none;">
                            @endif
                        </div>
                    </div>
                    <input type="hidden" name="more[qr_code]" value="{{$model->more['qr_code'] ?? ''}}" id="qr-code-path">
                    <div class="layui-word-aux st-form-tip layui-show">上传客服微信二维码，建议尺寸200x200像素</div>
                </div>
            </div>

            <div class="layui-form-item" id="contact-service-desc" style="display: none;">
                <label class="layui-form-label">支付说明</label>
                <div class="layui-input-block">
                    <textarea name="more[description]" placeholder="请输入支付说明文字" class="layui-textarea" rows="6">{{$model->more['description'] ?? ''}}</textarea>
                    <div class="layui-word-aux st-form-tip layui-show">向客户展示的支付说明文字，支持换行</div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><i class="layui-icon layui-icon-help st-form-tip-help"></i>手续费</label>
                <div class="layui-input-block">
                    <input type="text" name="more[rate]" value="{{$model->more['rate'] ?? ''}}" autocomplete="off" placeholder="5% 按比例收取； 20 按金额收取" class="layui-input">
                    <div class="layui-word-aux st-form-tip layui-show">如果包含 % 则按比例收取，否则按该数字金额收取</div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><i class="layui-icon layui-icon-help st-form-tip-help"></i>结算货币</label>
                <div class="layui-input-block">
                    <input type="text" name="more[currency]" value="{{$model->more['currency'] ?? ''}}" autocomplete="off" placeholder="" class="layui-input">
                    <div class="layui-word-aux st-form-tip layui-show">结算货币，不设置则自动识别</div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><i class="layui-icon layui-icon-help st-form-tip-help"></i>环境</label>
                <div class="layui-input-block">
                    <select name="more[env]">
                        <option value="sandbox" @if(isset($model->more['env']) && $model->more['env']=='sandbox') selected @endif > Sandbox 沙盒测试环境 </option>
                        <option value="live" @if(isset($model->more['env']) && $model->more['env']=='live') selected @endif > 正式环境 </option>
                    </select>
                    <div class="layui-word-aux st-form-tip layui-show">如果是 正式环境，请修改`收款账号`为正式商户收款账号</div>
                </div>
            </div>
        </div>
    </div>
    <div class="layui-form-item st-form-submit-btn">
        <div class="layui-input-block">
            <button type="submit" class="layui-btn" lay-submit="" lay-filter="ST-SUBMIT">立即提交</button>
            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
        </div>
    </div>
</form>
@endsection

@push('scripts_bottom')
<script>
    !function () {
        // 根据支付方式代码显示相应的配置字段
        function togglePaymentFields() {
            var code = $('input[name="code"]').val();

            // 隐藏所有特定配置字段
            $('#paypal-business').hide();
            $('#paypal-client-id').hide();
            $('#paypal-client-secret').hide();
            $('#paypal-test-connection').hide();
            $('#contact-service-qr').hide();
            $('#contact-service-desc').hide();

            // 根据代码显示相应字段
            if (code === 'paypal') {
                $('#paypal-business').show();
            } else if (code === 'paypal_new') {
                $('#paypal-client-id').show();
                $('#paypal-client-secret').show();
                $('#paypal-test-connection').show();
            } else if (code === 'contact_service') {
                $('#contact-service-qr').show();
                $('#contact-service-desc').show();
            }
        }

        // 页面加载时执行
        togglePaymentFields();

        // 当代码字段改变时执行
        $('input[name="code"]').on('input', togglePaymentFields);

        // PayPal连接测试功能
        $('#test-paypal-btn').on('click', function() {
            var btn = $(this);
            var resultDiv = $('#paypal-test-result');

            // 获取配置信息
            var clientId = $('input[name="more[client_id]"]').val();
            var clientSecret = $('input[name="more[client_secret]"]').val();
            var env = $('select[name="more[env]"]').val();

            // 验证必填字段
            if (!clientId || !clientSecret) {
                layer.msg('请先填写Client ID和Client Secret', {icon: 2});
                return;
            }

            // 显示加载状态
            btn.prop('disabled', true);
            btn.html('<i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i> 测试中...');
            resultDiv.html('<div class="layui-bg-blue" style="padding: 10px; border-radius: 4px; color: white;">正在测试PayPal连接...</div>');

            // 发送测试请求
            $.ajax({
                url: '/strongadmin/payment-option/test-paypal-connection',
                type: 'POST',
                data: {
                    client_id: clientId,
                    client_secret: clientSecret,
                    env: env,
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    btn.prop('disabled', false);
                    btn.html('<i class="layui-icon layui-icon-link"></i> 测试PayPal连接');

                    if (response.code === 200) {
                        resultDiv.html('<div class="layui-bg-green" style="padding: 10px; border-radius: 4px; color: white;">' +
                            '<i class="layui-icon layui-icon-ok"></i> ' + response.message + '<br>' +
                            '<small>环境: ' + (response.data.environment || 'N/A') + ' | Client ID: ' + (response.data.client_id || 'N/A') + '</small>' +
                            '</div>');
                        layer.msg('PayPal连接测试成功！', {icon: 1});
                    } else {
                        resultDiv.html('<div class="layui-bg-red" style="padding: 10px; border-radius: 4px; color: white;">' +
                            '<i class="layui-icon layui-icon-close"></i> ' + response.message +
                            '</div>');
                        layer.msg('PayPal连接测试失败', {icon: 2});
                    }
                },
                error: function(xhr, status, error) {
                    btn.prop('disabled', false);
                    btn.html('<i class="layui-icon layui-icon-link"></i> 测试PayPal连接');

                    var errorMsg = '连接测试失败';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMsg = xhr.responseJSON.message;
                    }

                    resultDiv.html('<div class="layui-bg-red" style="padding: 10px; border-radius: 4px; color: white;">' +
                        '<i class="layui-icon layui-icon-close"></i> ' + errorMsg +
                        '</div>');
                    layer.msg('请求失败: ' + error, {icon: 2});
                }
            });
        });

        // 文件上传功能
        layui.upload.render({
            elem: '#qr-upload',
            url: '/strongadmin/upload/image',
            accept: 'images',
            acceptMime: 'image/*',
            size: 2048, // 2MB
            done: function(res){
                console.log('上传返回结果:', res); // 调试用
                if(res.code === 0){ // 修正：上传接口返回的是 code: 0
                    var imagePath = res.data.src; // 获取图片路径
                    var imageUrl = '/storage' + imagePath; // 构建完整URL
                    $('#qr-preview').attr('src', imageUrl).show();
                    $('#qr-code-path').val(imagePath.replace('/storage/', '')); // 保存相对路径

                    // 显示删除按钮
                    if ($('#qr-delete').length === 0) {
                        $('#qr-upload').after('<button type="button" class="layui-btn layui-btn-danger" id="qr-delete" style="margin-left: 10px;">删除二维码</button>');
                        bindDeleteEvent();
                    }

                    layer.msg('上传成功');
                } else {
                    layer.msg('上传失败：' + (res.message || '未知错误'));
                }
            },
            error: function(){
                layer.msg('上传失败，请重试');
            }
        });

        // 删除二维码功能
        function bindDeleteEvent() {
            $(document).off('click', '#qr-delete').on('click', '#qr-delete', function(){
                layer.confirm('确认删除该二维码？删除后将同时删除服务器上的图片文件。', function(index){
                    var qrCodePath = $('#qr-code-path').val();
                    if (!qrCodePath) {
                        layer.msg('没有可删除的二维码');
                        return;
                    }

                    // 发送删除请求
                    $.post('/strongadmin/payment-option/delete-qr-code', {
                        qr_code_path: qrCodePath,
                        _token: $('meta[name="csrf-token"]').attr('content')
                    }).then(response => {
                        if (response.code === 200) {
                            // 清空前端显示
                            $('#qr-preview').hide().attr('src', '');
                            $('#qr-code-path').val('');
                            $('#qr-delete').remove();
                            layer.close(index);
                            layer.msg(response.message);
                        } else {
                            layer.msg(response.message, {anim: 6});
                        }
                    }).catch(error => {
                        layer.msg('删除失败，请重试', {anim: 6});
                    });
                });
            });
        }

        // 页面加载时绑定删除事件（如果有现有图片）
        if ($('#qr-delete').length > 0) {
            bindDeleteEvent();
        }
    }();
</script>
@endpush
