<?php $__env->startPush('styles'); ?>
<style>
    /*.layui-form-label{width:150px;}*/
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script></script>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="st-h15"></div>
<form class="layui-form" action="">
    <input name="id" type="hidden" value="<?php echo e($model->id); ?>" />
    <input name="shipping_option_id" type="hidden" value="<?php echo e($model->shipping_option_id ?: request('shipping_option_id')); ?>" />
    <div class="layui-tab layui-tab-brief">
        <ul class="layui-tab-title">
            <li class="layui-this">基础配置</li>
            <li>国家</li>
        </ul>
        <div class="layui-tab-content">
            <!--基础信息-->
            <div class="layui-tab-item layui-show">
                <div class="layui-form-item">
                    <label class="layui-form-label st-form-input-required"><?php echo e($model->getAttributeLabel('title')); ?></label>
                    <div class="layui-input-block">
                        <input type="text" name="title" value="<?php echo e($model->title); ?>" autocomplete="off" placeholder="" class="layui-input">
                        <div class="layui-word-aux st-form-tip"></div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label st-form-input-required"><?php echo e($model->getAttributeLabel('status')); ?></label>
                    <div class="layui-input-block">
                        <input type="radio" name="status" value="1" title="启用" <?php if($model->status==1): ?>checked <?php endif; ?>>
                        <input type="radio" name="status" value="2" title="禁用" <?php if($model->status==2): ?>checked <?php endif; ?>>
                        <div class="layui-word-aux st-form-tip"></div>
                    </div>
                </div>
                <hr/>

                <!-- 免运费阈值配置 -->
                <fieldset class="layui-elem-field" style="margin-top: 20px;">
                    <legend>免运费设置</legend>
                    <div class="layui-field-box">
                        <div class="layui-form-item">
                            <label class="layui-form-label">启用免运费</label>
                            <div class="layui-input-block">
                                <input type="checkbox" name="more[free_shipping_threshold][enabled]" value="1"
                                       <?php if(isset($model->more['free_shipping_threshold']['enabled']) && $model->more['free_shipping_threshold']['enabled']): ?> checked <?php endif; ?>
                                       lay-skin="switch" lay-text="启用|禁用">
                                <div class="layui-word-aux st-form-tip">启用后，当重量达到设定阈值时将免收运费</div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">免运费重量阈值</label>
                            <div class="layui-input-inline">
                                <input type="text" name="more[free_shipping_threshold][weight]"
                                       value="<?php echo e($model->more['free_shipping_threshold']['weight'] ?? ''); ?>"
                                       autocomplete="off" placeholder="如：22000（表示22公斤）" class="layui-input">
                            </div>
                            <div class="layui-form-mid">克</div>
                            <div class="layui-word-aux st-form-tip">当商品重量大于等于此值时免收运费（单位：克）</div>
                        </div>
                    </div>
                </fieldset>

                <p>
                    <a href="<?php echo e(request()->fullUrlWithQuery(['create'=>1])); ?>" class="layui-btn">新增公式</a>
                </p>
                <?php if(isset($model->more['range_wieght'])): ?>
                <?php $__currentLoopData = $model->more['range_wieght']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $wieght): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <fieldset class="layui-elem-field" style="margin-top: 30px;">
                    <legend>公式<?php echo e($loop->iteration); ?> <a href="<?php echo e(request()->fullUrlWithQuery(['removeRule'=>$loop->index])); ?>" class="layui-btn layui-btn-xs">删除公式</a></legend>
                    <div class="layui-field-box">
                        <div class="layui-form-item">
                            <label class="layui-form-label st-form-input-required">重量范围（g）</label>
                            <div class="layui-input-inline">
                                <input type="text" name="more[range_wieght][<?php echo e($loop->index); ?>][range][start]" value="<?php echo e($wieght['range']['start']); ?>" <?php echo e($loop->index ==0 ? "readonly" : ""); ?> autocomplete="off" placeholder="起始值" class="layui-input">
                            </div>
                            <div class="layui-form-mid">-</div>
                            <div class="layui-input-inline">
                                <input type="text" name="more[range_wieght][<?php echo e($loop->index); ?>][range][end]" value="<?php echo e($wieght['range']['end']); ?>" autocomplete="off" placeholder="结束值" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label st-form-input-required">首重($ / g)</label>
                            <div class="layui-input-inline">
                                <input type="text" name="more[range_wieght][<?php echo e($loop->index); ?>][first_weight][amount]" value="<?php echo e($wieght['first_weight']['amount']); ?>" autocomplete="off" placeholder="费用" class="layui-input">
                            </div>
                            <div class="layui-form-mid"><?php echo e(config('strongshop.defaultCurrencyBackend')); ?> / </div>
                            <div class="layui-input-inline">
                                <input type="text" name="more[range_wieght][<?php echo e($loop->index); ?>][first_weight][weight]" value="<?php echo e($wieght['first_weight']['weight']); ?>" autocomplete="off" placeholder="重量" class="layui-input">
                            </div>
                            <div class="layui-form-mid">克</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">续重($ / g)</label>
                            <div class="layui-input-inline">
                                <input type="text" name="more[range_wieght][<?php echo e($loop->index); ?>][continued_weight][amount]" value="<?php echo e($wieght['continued_weight']['amount']); ?>" autocomplete="off" placeholder="费用（可设为0）" class="layui-input">
                            </div>
                            <div class="layui-form-mid"><?php echo e(config('strongshop.defaultCurrencyBackend')); ?> / </div>
                            <div class="layui-input-inline">
                                <input type="text" name="more[range_wieght][<?php echo e($loop->index); ?>][continued_weight][weight]" value="<?php echo e($wieght['continued_weight']['weight']); ?>" autocomplete="off" placeholder="重量（可设为0）" class="layui-input">
                            </div>
                            <div class="layui-form-mid">克</div>
                            <div class="layui-word-aux st-form-tip">续重费用和重量都可以设置为0，表示只收取首重费用</div>
                        </div>
                    </div>
                </fieldset>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <div class="layui-form-item">
                    <label class="layui-form-label st-form-input-required">超出范围($/g)</label>
                    <div class="layui-input-inline">
                        <input type="text" name="more[over_weight][amount]" value="<?php echo e($model->more['over_weight']['amount']); ?>" autocomplete="off" placeholder="费用" class="layui-input">
                    </div>
                    <div class="layui-form-mid"><?php echo e(config('strongshop.defaultCurrencyBackend')); ?> / </div>
                    <div class="layui-input-inline">
                        <input type="text" name="more[over_weight][weight]" value="<?php echo e($model->more['over_weight']['weight']); ?>" autocomplete="off" placeholder="重量" class="layui-input">
                    </div>
                    <div class="layui-form-mid">克</div>
                </div>
                <?php endif; ?>
            </div>
            <!--配置信息-->
            <div class="layui-tab-item">
                <?php $__currentLoopData = $countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <span style="display: inline-block;width:300px;overflow: hidden;">
                    <input <?php if($model->countries && in_array($country->iso2,$model->countries)): ?> checked="" <?php endif; ?> type="checkbox" name="countries[]" lay-skin="primary" title="<?php echo e($country->en_name); ?> <?php echo e($country->cn_name); ?>" value="<?php echo e($country->iso2); ?>">
                </span>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            </div>
        </div>
    </div>
    <div class="layui-form-item st-form-submit-btn">
        <div class="layui-input-block">
            <button type="submit" class="layui-btn" lay-submit="" lay-filter="ST-SUBMIT">立即提交</button>
            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
        </div>
    </div>
</form>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts_bottom'); ?>
<script>
    !function () {
        $("fieldset>legend>button").click(function () {
            $(this).parent().parent().remove();
        });
        layui.form.on('submit(ST-SUBMIT)', function (data) {
            Util.postForm('form.layui-form', data.field, false);
            return false;
        });
    }();
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('strongadmin::layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\wwwroot\shiptobuy_guanwang\resources\views/strongadmin/shippingOptionConfig/form.blade.php ENDPATH**/ ?>