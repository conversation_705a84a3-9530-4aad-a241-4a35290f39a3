<p class="st-total">
    <span><?php echo app('translator')->get('Cart Subtotal'); ?>:</span>
    <strong><?php echo e($_current_currency_name); ?> <span class="ST-cart-cart_total"><?php echo e($_cart['total']['cart_total']); ?></span></strong>
</p>
<?php if($_cart['total']['shipping_total'] !== false): ?>
<?php if(config('strongshop.showCountriesInCart')): ?>
<p class="st-total">
    <span><?php echo app('translator')->get('Shipping To'); ?>:</span>
    <select style="width:35%" name="_country_code" onchange="Util.fetchShoppingcartHtml();">
        <?php $__currentLoopData = $_countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $_countryCode=>$_country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <option value="<?php echo e($_countryCode); ?>" <?php if($_countryCode === $_user_country_code): ?> selected <?php endif; ?>><?php echo e($_country); ?></option>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </select>
</p>
<?php endif; ?>
<?php if(config('strongshop.showShipinglistInCart')): ?>
<p class="st-total">
    <span><?php echo app('translator')->get('Shipping Options'); ?>:</span>
    <select style="width:35%" name="_shipping_option_id" onchange="Util.fetchShoppingcartHtml();">
        <?php $__currentLoopData = $_shipping_options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $_shipping_option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <option value="<?php echo e($_shipping_option['id']); ?>"><?php echo e($_shipping_option['title']); ?></option>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </select>
</p>
<?php endif; ?>
<p class="st-total">
    <span><?php echo app('translator')->get('Shipping Cost'); ?>:</span>
    <strong><?php echo e($_current_currency_name); ?> <span class="ST-cart-shipping_total"><?php echo e($_cart['total']['shipping_total']); ?></span></strong>
</p>
<?php endif; ?>
<p>
    <a href="<?php echo e(route('shoppingcart.checkout')); ?>" class="btn btn-warning"><?php echo app('translator')->get('Proceed to checkout'); ?></a>
</p><?php /**PATH D:\wwwroot\shiptobuy_guanwang\resources\views\themes\default/layouts/includes/shoppingcartBtn.blade.php ENDPATH**/ ?>